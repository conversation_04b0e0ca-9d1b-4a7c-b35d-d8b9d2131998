<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!--[+] define the ui config for entity -->
    <virtualType name="ComaveIntegrationTraceabilityUiConfig" type="Umc\Crud\Ui\EntityUiConfig">
        <arguments>
            <argument name="interface" xsi:type="string">Comave\Integration\Api\Data\TraceInterface</argument>
            <argument name="data" xsi:type="array">
                <item name="name_attribute" xsi:type="string">
                    endpoint
                </item><!-- this is the attribute / field name that represents your entity -->
                <item name="labels" xsi:type="array">
                    <item name="new" xsi:type="string" translatable="true">Add New Trace</item>
                    <item name="back" xsi:type="string" translatable="true">Back to list</item>
                    <item name="save" xsi:type="string" translatable="true">Save Trace</item>
                    <item name="delete" xsi:type="string" translatable="true">Delete Trace</item>
                    <item name="delete_message" xsi:type="string" translatable="true">
                        Are you sure you want to delete this integration tarce?
                    </item>
                </item>
                <item name="save" xsi:type="array">
                    <item name="allow_close" xsi:type="boolean">false</item>
                    <item name="allow_duplicate" xsi:type="boolean">false</item>
                </item>
                <item name="list" xsi:type="array">
                    <item name="page_title" xsi:type="string" translatable="true">Integration Traceability</item>
                </item>
                <item name="messages" xsi:type="array">
                    <item name="delete" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">
                            Trace successfully deleted
                        </item>
                        <item name="missing_entity" xsi:type="string" translatable="true">
                            Couldn't find the integration tarce to delete
                        </item>
                        <item name="error" xsi:type="string" translatable="true">
                            There was a problem deleting the integration tarce
                        </item>
                    </item>
                    <item name="save" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">
                            Integration Tarce successfully saved
                        </item>
                        <item name="error" xsi:type="string" translatable="true">
                            There was a problem saving the integration tarce
                        </item>
                        <item name="duplicate" xsi:type="string" translatable="true">
                            Integration Tarce duplicated successfully
                        </item>
                    </item>
                    <item name="mass_delete" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">
                            %1 Integration Tarces were successfully deleted
                        </item>
                        <item name="error" xsi:type="string" translatable="true">
                            There was a problem deleting these integration tarces
                        </item>
                    </item>
                </item>
            </argument>
        </arguments>
    </virtualType>
    <!--[-] define the ui config for "media" entity -->
    <!--[+] configure admin controllers to use the ui entity config for "media" -->
    <virtualType name="ComaveIntegrationTraceabilityDataModifier"
                 type="Umc\Crud\Ui\Form\DataModifier\CompositeDataModifier">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="product" xsi:type="object">ComaveIntegrationTraceabilityProductDataModifier</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveIntegrationTraceabilitySaveDataProcessor"
                 type="Umc\Crud\Ui\SaveDataProcessor\CompositeProcessor">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="product" xsi:type="object">ComaveIntegrationTraceabilityProductProcessor</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveIntegrationTraceabilityProductProcessor"
                 type="Comave\Catalog\Ui\SaveDataProcessor\Product">
        <arguments>
            <argument name="fields" xsi:type="array">
                <item name="product_sku" xsi:type="string">product_sku</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveIntegrationTraceabilityProductDataModifier"
                 type="Comave\Catalog\Ui\Form\DataModifier\Product">
        <arguments>
            <argument name="fields" xsi:type="array">
                <item name="product_sku" xsi:type="string">product_sku</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Comave\Integration\Controller\Adminhtml\Traceability\Index">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveIntegrationTraceabilityUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\Integration\Controller\Adminhtml\Traceability\Edit">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveIntegrationTraceabilityUiConfig</argument>
            <argument name="entityUiManager" xsi:type="object">Comave\Integration\Model\TraceUiManager
            </argument>
        </arguments>
    </type>
    <type name="Comave\Integration\Controller\Adminhtml\Traceability\Save">
        <arguments>
            <argument name="entityUiManager" xsi:type="object">Comave\Integration\Model\TraceUiManager
            </argument>
            <argument name="dataProcessor" xsi:type="object">ComaveIntegrationTraceabilitySaveDataProcessor</argument>
            <argument name="uiConfig" xsi:type="object">ComaveIntegrationTraceabilityUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\Integration\Controller\Adminhtml\Traceability\InlineEdit">
        <arguments>
            <argument name="entityUiManager" xsi:type="object">Comave\Integration\Model\TraceUiManager
            </argument>
            <argument name="dataProcessor" xsi:type="object">Umc\Crud\Ui\SaveDataProcessor\NullProcessor</argument>
            <argument name="uiConfig" xsi:type="object">ComaveIntegrationTraceabilityUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\Integration\Controller\Adminhtml\Traceability\Delete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveIntegrationTraceabilityUiConfig</argument>
            <argument name="uiManager" xsi:type="object">Comave\Integration\Model\TraceUiManager</argument>
        </arguments>
    </type>
    <type name="Comave\Integration\Controller\Adminhtml\Traceability\MassDelete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveIntegrationTraceabilityUiConfig</argument>
            <argument name="uiManager" xsi:type="object">Comave\Integration\Model\TraceUiManager</argument>
            <argument name="collectionProvider" xsi:type="object">
                Comave\Integration\Model\TraceUiCollectionProvider
            </argument>
        </arguments>
    </type>
    <!--[+] configure admin controllers to use the ui entity config for "process" -->
    <virtualType name="ComaveIntegrationTraceabilityUiFormDataProvider" type="Umc\Crud\Ui\Form\DataProvider">
        <arguments>
            <argument name="primaryFieldName" xsi:type="object">trace_id</argument>
            <argument name="requestFieldName" xsi:type="object">trace_id</argument>
            <argument name="uiConfig" xsi:type="object">ComaveIntegrationTraceabilityUiConfig</argument>
            <argument name="dataModifier" xsi:type="object">ComaveIntegrationTraceabilityDataModifier</argument>
            <argument name="collectionProvider" xsi:type="object">
                Comave\Integration\Model\TraceUiCollectionProvider
            </argument>
        </arguments>
    </virtualType>
    <!--[+] form button configuration for 'process' -->
    <virtualType name="ComaveIntegrationTraceabilityButtonBack" type="Umc\Crud\Block\Adminhtml\Button\Back">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveIntegrationTraceabilityUiConfig</argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveIntegrationTraceabilityButtonSave" type="Umc\Crud\Block\Adminhtml\Button\Save">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveIntegrationTraceabilityUiConfig</argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveIntegrationTraceabilityButtonDelete" type="Umc\Crud\Block\Adminhtml\Button\Delete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveIntegrationTraceabilityUiConfig</argument>
            <argument name="entityUiManager" xsi:type="object">Comave\Integration\Model\TraceUiManager
            </argument>
        </arguments>
    </virtualType>
    <!--[-] form button configuration for 'process' -->
    <!--[+] configure the grid actions column  for "process" entity-->
    <virtualType name="ComaveIntegrationTraceabilityGridActions" type="Umc\Crud\Ui\Component\Listing\ActionsColumn">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveIntegrationTraceUiConfig</argument>
        </arguments>
    </virtualType>
    <!--[-] configure the grid actions column for "process" entity-->
</config>
