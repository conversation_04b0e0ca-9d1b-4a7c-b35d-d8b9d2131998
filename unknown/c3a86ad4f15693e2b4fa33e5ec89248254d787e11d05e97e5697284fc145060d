<?php
declare(strict_types=1);

namespace Comave\Twilio\Helper;

use Magento\Framework\App\Config\ScopeConfigInterface;

class TwilioConfig
{
    private const XML_ACCOUNT_SID       = 'twilio/general/account_sid';
    private const XML_AUTH_TOKEN        = 'twilio/general/auth_token';
    private const XML_VERIFY_SERVICE_ID = 'twilio/general/verify_service_sid';

    public function __construct(private ScopeConfigInterface $scopeConfig) {}

    public function getAccountSid(): string
    {
        return (string)$this->scopeConfig->getValue(self::XML_ACCOUNT_SID);
    }

    public function getAuthToken(): string
    {
        return (string)$this->scopeConfig->getValue(self::XML_AUTH_TOKEN);
    }

    public function getVerifyServiceSid(): string
    {
        return (string)$this->scopeConfig->getValue(self::XML_VERIFY_SERVICE_ID);
    }
}
