<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="comave_integration_token" resource="default" engine="innodb" comment="Integration Admin Tokens">
        <column name="token_id" xsi:type="int" unsigned="true" nullable="false" identity="true" comment="Token ID"/>
        <column name="user_id" xsi:type="int" unsigned="true" nullable="false" comment="User ID"/>
        <column name="user_type" xsi:type="int" unsigned="true" nullable="false" comment="User Type"/>
        <column name="encrypted_token" xsi:type="text" nullable="false" comment="Encrypted Token"/>
        <column name="created_at" xsi:type="timestamp" nullable="false" default="CURRENT_TIMESTAMP" comment="Creation Time"/>
        <column name="updated_at" xsi:type="timestamp" nullable="false" default="CURRENT_TIMESTAMP" comment="Update Time"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="token_id"/>
        </constraint>
    </table>
    <table name="comave_integration_traceability" resource="default" engine="innodb" comment="Integration Trace">
        <column name="trace_id" xsi:type="int" unsigned="true" nullable="false" identity="true" comment="Trace ID"/>
        <column name="method" xsi:type="varchar" length="32" nullable="false" comment="Method"/>
        <column name="endpoint" xsi:type="varchar" length="255" nullable="false" comment="Endpoint"/>
        <column xsi:type="json" name="payload" nullable="false" comment="Payload"/>
        <column name="created_at" xsi:type="timestamp" nullable="false" default="CURRENT_TIMESTAMP" comment="Creation Time"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="trace_id"/>
        </constraint>
    </table>
</schema>
