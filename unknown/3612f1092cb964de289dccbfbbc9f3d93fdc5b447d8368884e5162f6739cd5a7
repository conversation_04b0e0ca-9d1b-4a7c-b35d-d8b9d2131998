<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="Webkul_MpSellerCategory::css/styles.css"/>
        <css src="Webkul_Marketplace::css/wk_block.css"/>
        <css src="Webkul_Marketplace::css/style.css"/>
        <css src="Webkul_Marketplace::css/product.css"/>
        <css src="Webkul_Marketplace::css/layout.css"/>
        <css src="Comave_SellerPayouts::css/styles.css"/>
    </head>
    <body>
        <referenceBlock name="seller.page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Manage Stripe Accounts</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="seller.content">
            <block class="Comave\SellerPayouts\Block\StripeDetails" name="seller_payouts_manage" before="-" template="Comave_SellerPayouts::seller/stripeconnect.phtml">
                <arguments>
                    <argument name="adminImpersonation" xsi:type="object">Comave\Marketplace\ViewModel\AdminImpersonation</argument>
                </arguments>
            </block>
       </referenceContainer>
    </body>
</page>
