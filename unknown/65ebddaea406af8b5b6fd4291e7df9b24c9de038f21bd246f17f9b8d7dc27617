<?xml version="1.0"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">
                comave_integration_traceability_listing.comave_integration_traceability_listing_data_source
            </item>
        </item>
    </argument>
    <settings>
        <spinner>comave_integration_trace_columns</spinner>
        <deps>
            <dep>comave_integration_traceability_listing.comave_integration_traceability_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource name="comave_integration_traceability_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">trace_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider"
                      name="comave_integration_traceability_listing_data_source">
            <settings>
                <requestFieldName>trace_id</requestFieldName>
                <primaryFieldName>trace_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="observers" xsi:type="array">
                    <item name="column" xsi:type="string">column</item>
                </item>
            </argument>
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
        </filters>
        <massaction name="listing_massaction">
            <action name="delete">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to delete the selected integration traces?</message>
                        <title translate="true">Delete</title>
                    </confirm>
                    <url path="comave_integration/traceability/massDelete"/>
                    <type>delete</type>
                    <label translate="true">Delete</label>
                </settings>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="comave_integration_trace_columns">
        <selectionsColumn name="ids">
            <settings>
                <indexField>trace_id</indexField>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>20</resizeDefaultWidth>
            </settings>
        </selectionsColumn>
        <column name="trace_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
                <visible>false</visible>
            </settings>
        </column>
        <column name="method">
            <settings>
                <filter>text</filter>
                <label translate="true">Method</label>
                <draggable>false</draggable>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>20</resizeDefaultWidth>
            </settings>
        </column>
        <column name="endpoint">
            <settings>
                <filter>text</filter>
                <label translate="true">Endpoint</label>
                <draggable>false</draggable>
                <sortable>true</sortable>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>50</resizeDefaultWidth>
            </settings>
        </column>
        <column name="payload">
            <settings>
                <filter>text</filter>
                <label translate="true">Payload</label>
                <draggable>false</draggable>
                <sortable>true</sortable>
                <resizeEnabled>true</resizeEnabled>
            </settings>
        </column>
        <actionsColumn name="actions" class="Comave\Integration\Ui\Component\Listing\Column\Traceability\Actions">
            <settings>
                <indexField>trace_id</indexField>
                <resizeEnabled>false</resizeEnabled>
            </settings>
        </actionsColumn>
    </columns>
</listing>