<?php
declare(strict_types=1);

namespace Comave\Twilio\Model;

use Twilio\Exceptions\RestException;
use Twilio\Rest\Client;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;
use Comave\Twilio\Api\Data\VerificationInterface;
use Comave\Twilio\Model\ResourceModel\Verification as VerificationResource;
use Comave\Twilio\Model\VerificationFactory;
use Comave\Twilio\Helper\TwilioConfig as Config;

class PhoneVerification
{
    public const STATUS_PENDING  = 'pending';
    public const STATUS_VERIFIED = 'verified';
    public const STATUS_FAILED   = 'failed';

    private Client $twilio;
    private string $verifyServiceSid;

    public function __construct(
        private readonly Config $config,
        private readonly VerificationFactory $verificationFactory,
        private readonly VerificationResource $verificationResource,
        private readonly LoggerInterface $logger
    ) {
        $this->twilio = new Client(
            $this->config->getAccountSid(),
            $this->config->getAuthToken()
        );
        $this->verifyServiceSid = $this->config->getVerifyServiceSid();
    }

    public function sendVerificationCode(int $customerId, string $phone): VerificationInterface
    {
        try {
            $twilioResp = $this->twilio
                ->verify
                ->v2
                ->services($this->verifyServiceSid)
                ->verifications
                ->create($phone, 'sms');
        } catch (RestException $e) {
            $this->logger->critical('Twilio REST error (send): ' . $e->getMessage(), ['exception' => $e]);
            throw new LocalizedException(__('Could not send verification code. Please try again.'));
        } catch (\Throwable $e) {
            $this->logger->critical('Unexpected error (send): ' . $e->getMessage(), ['exception' => $e]);
            throw new LocalizedException(__('Something went wrong while sending the code.'));
        }

        $row = $this->verificationFactory->create();
        $row->setData([
            'customer_id'      => $customerId,
            'phone_number'     => $phone,
            'verification_sid' => $twilioResp->sid,
            'status'           => self::STATUS_PENDING,
        ]);

        try {
            $this->verificationResource->save($row);
        } catch (\Throwable $e) {
            $this->logger->critical('DB save error (send): ' . $e->getMessage(), ['exception' => $e]);
            throw new LocalizedException(__('Could not persist verification request.'));
        }

        return $row;
    }

    public function verifyCode(string $phone, string $code): VerificationInterface
    {
        try {
            $check = $this->twilio
                ->verify
                ->v2
                ->services($this->verifyServiceSid)
                ->verificationChecks
                ->create([
                    'to'   => $phone,
                    'code' => $code,
                ]);
        } catch (RestException $e) {
            $this->logger->critical('Twilio REST error (verify): ' . $e->getMessage(), ['exception' => $e]);
            throw new LocalizedException(__('Verification failed. Incorrect code or phone.'));
        } catch (\Throwable $e) {
            $this->logger->critical('Unexpected error (verify): ' . $e->getMessage(), ['exception' => $e]);
            throw new LocalizedException(__('Could not verify the code. Please try again.'));
        }

        $row = $this->verificationResource->getLatestByPhone($phone);

        if (!$row->getId()) {
            throw new LocalizedException(__('Verification attempt not found.'));
        }

        $row->setStatus(
            $check->status === 'approved' ? self::STATUS_VERIFIED : self::STATUS_FAILED
        );

        if ($row->getStatus() === self::STATUS_VERIFIED) {
            $row->setVerifiedAt(date('Y-m-d H:i:s'));
        }

        try {
            $this->verificationResource->save($row);
        } catch (\Throwable $e) {
            $this->logger->critical('DB save error (verify): ' . $e->getMessage(), ['exception' => $e]);
            throw new LocalizedException(__('Could not update verification status.'));
        }

        return $row;
    }

    public function getStatus(string $phone): VerificationInterface
    {
        return $this->verificationResource->getLatestByPhone($phone);
    }
}
