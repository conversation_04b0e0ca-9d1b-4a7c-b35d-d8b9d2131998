<?xml version="1.0"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">
                comave_integration_traceability_form.comave_integration_traceability_form_data_source
            </item>
        </item>
        <item name="label" xsi:type="string" translate="true">Blacklist Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <buttons>
            <button name="back" class="ComaveIntegrationTraceabilityButtonBack"/>
        </buttons>
        <namespace>comave_integration_traceability_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>comave_integration_traceability_form.comave_integration_traceability_form_data_source</dep>
        </deps>
    </settings>
    <dataSource name="comave_integration_traceability_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="comave_integration/traceability/save"/>
        </settings>
        <dataProvider class="ComaveIntegrationTraceabilityUiFormDataProvider"
                      name="comave_integration_traceability_form_data_source"/>
    </dataSource>
    <fieldset name="traceability" sortOrder="10">
        <settings>
            <collapsible>false</collapsible>
            <opened>true</opened>
            <label translate="true"></label>
        </settings>
        <field name="method">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Method</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">method</item>
                    <item name="disabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </field>
        <field name="endpoint">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Endpoint</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">endpoint</item>
                    <item name="disabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </field>
        <field name="payload">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Payload</item>
                    <item name="formElement" xsi:type="string">textarea</item>
                    <item name="source" xsi:type="string">payload</item>
                    <item name="disabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </field>
    </fieldset>
</form>
