<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Club\Setup\Patch\Data;

use Comave\Marketplace\Model\FixtureManager;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class InstallClubRecordsRomaBarca implements DataPatchInterface
{
    /**
     * @param \Magento\Framework\Setup\ModuleDataSetupInterface $moduleDataSetup
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly FixtureManager $fixtureManager
    ) {
    }

    /**
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->startSetup();
        $clubGroup = $this->getLastInsertedIdClubGroup();
        $clubs = $this->insertClubs($clubGroup);
        $this->setClubStore($clubs);
        $this->moduleDataSetup->endSetup();
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return int
     */
    private function getLastInsertedIdClubGroup(): int
    {
        $select = $this->moduleDataSetup->getConnection()->select();
        $select->from(
            $this->moduleDataSetup->getConnection()->getTableName('comave_club_group')
        )->order('group_id DESC');
        return (int)$this->moduleDataSetup->getConnection()->fetchOne($select);
    }

    /**
     * @param int $clubGroup
     * @return array
     */
    private function insertClubs(int $clubGroup): array
    {
        $this->fixtureManager->setModuleName('Comave_Club');
        $clubs = $this->fixtureManager->getData('roma_barcelona_clubs');
        foreach ($clubs as $index => $club) {
            $clubs[$index]['group_id'] = $clubGroup;
        }
        $table = $this->moduleDataSetup->getConnection()->getTableName('comave_club');
        $this->moduleDataSetup->getConnection()->insertMultiple($table, $clubs);
        return $clubs;
    }

    /**
     * @param $clubs
     * @return void
     */
    private function setClubStore($clubs): void
    {
        $uniqueIds = [];
        foreach ($clubs as $club) {
            $uniqueIds[] = $club['uniqueid'];
        }
        $insertData = $this->getClubs($uniqueIds);
        $table = $this->moduleDataSetup->getConnection()->getTableName('comave_club_store');
        $this->moduleDataSetup->getConnection()->insertMultiple($table, $insertData);
    }

    /**
     * @param $uniqueIds
     * @return array
     */
    private function getClubs($uniqueIds): array
    {
        $clubs = [];
        $select = $this->moduleDataSetup->getConnection()->select();
        $select->from(
            $this->moduleDataSetup->getConnection()->getTableName('comave_club')
        )->where('uniqueid IN (?)', $uniqueIds);

        $data = $this->moduleDataSetup->getConnection()->fetchAll($select);
        foreach ($data as $row) {
            $clubs[] = [
                'club_id' => $row['club_id'],
                'store_id' => 0
            ];
        }

        return $clubs;
    }
}
