<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Api\Data;

use Magento\Framework\Api\SearchCriteriaInterface;

interface TraceSearchResultInterface
{
    /**
     * get items
     *
     * @return \Comave\Integration\Api\Data\TraceInterface[]
     */
    public function getItems();

    /**
     * Set items
     *
     * @param \Comave\Integration\Api\Data\TraceInterface[] $items
     * @return $this
     */
    public function setItems(array $items);

    /**
     * @param SearchCriteriaInterface $searchCriteria
     * @return $this
     */
    public function setSearchCriteria(SearchCriteriaInterface $searchCriteria);

    /**
     * @param int $count
     * @return $this
     */
    public function setTotalCount($count);
}
