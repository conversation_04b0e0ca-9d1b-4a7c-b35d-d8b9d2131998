<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\MapOrderStatuses\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Store\Model\ScopeInterface;

class ConfigProvider
{
    public const string BIGBUY_PROVIDER = 'bigbuy';
    public const string SHOPIFY_PROVIDER = 'shopify';
    private const string XML_PATH_GENERAL_ORDER_STATUS_MAPPING = 'comave_maporderstatuses/general/order_status_mapping';
    private const string XML_PATH_CUSTOM_ORDER_STATUS_MAPPING = 'comave_maporderstatuses/custom_statuses/order_status_custom_mapping';
    public const string CONFIG_PATH_BIGBUY_ORDER_STATUSES = 'seller_settings/bigbuy_api/order_statuses';

    /**
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     */
    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly SerializerInterface $serializer,
    ) {
    }

    /**
     * @param $provider
     * @param $status
     * @return string
     */
    public function getStatus($provider, $status): string
    {
        if (!in_array($provider, [self::BIGBUY_PROVIDER, self::SHOPIFY_PROVIDER])) {
            return '';
        }

        $mapping = match($provider) {
            self::BIGBUY_PROVIDER => $this->getBigBuyStatuses(),
            self::SHOPIFY_PROVIDER => $this->getShopifyStatuses(),
            default => $this->getConfigValue(self::XML_PATH_GENERAL_ORDER_STATUS_MAPPING)
        };

        $data = $this->serializer->unserialize($mapping);

        if (empty($data)) {
            return '';
        }


        foreach ($data as $statusEntry) {
            //shopifylogic
            if ($provider === self::SHOPIFY_PROVIDER && $statusEntry['provider_list'] === $provider && $statusEntry['provider_custom_status'] === $status) {
                return $statusEntry['magento_status'];
            }

            //bigbuylogic
            if ($provider === self::BIGBUY_PROVIDER && !empty($statusEntry['provider']) && !empty($statusEntry['provider_status']) && !empty($statusEntry['magento_status'])) {
                if ($statusEntry['provider'] === $provider && $statusEntry['provider_status'] === $status) {
                    return $statusEntry['magento_status'];
                }
            }
        }

        return '';
    }

    /**
     * @return string
     */
    public function getBigBuyStatuses(): string
    {
        return $this->getConfigValue(self::CONFIG_PATH_BIGBUY_ORDER_STATUSES);
    }

    /**
     * @return string
     */
    public function getShopifyStatuses(): string
    {
        return $this->getConfigValue(self::XML_PATH_CUSTOM_ORDER_STATUS_MAPPING);
    }

    /**
     * @param string $path
     * @return string
     */
    public function getConfigValue(string $path): string
    {
        return $this->scopeConfig->getValue(
            $path,
            ScopeInterface::SCOPE_STORES
        ) ?? '';
    }

    /**
     * @param string $status
     * @return bool
     */
    public function isPredefinedStatusMapped(string $status): bool
    {
        $configJson = $this->getConfigValue(self::XML_PATH_GENERAL_ORDER_STATUS_MAPPING);
        if (empty($configJson)) {
            return false;
        }

        $mappedStatuses = $this->serializer->unserialize($configJson);
        if (!is_array($mappedStatuses)) {
            return false;
        }

        return (bool) array_filter($mappedStatuses, static fn($mapping) =>
            is_array($mapping) &&
            ($mapping['provider_status'] ?? null) === $status
        );
    }

    /**
     * @param string $status
     * @return bool
     */
    public function isCustomStatusMapped(string $status): bool
    {
        $configJson = $this->getConfigValue(self::XML_PATH_CUSTOM_ORDER_STATUS_MAPPING);
        if (empty($configJson)) {
            return false;
        }

        $mappedStatuses = $this->serializer->unserialize($configJson);
        if (!is_array($mappedStatuses)) {
            return false;
        }

        return (bool) array_filter($mappedStatuses, static fn($mapping) =>
            is_array($mapping) &&
            ($mapping['provider_custom_status'] ?? null) === $status
        );
    }
}
