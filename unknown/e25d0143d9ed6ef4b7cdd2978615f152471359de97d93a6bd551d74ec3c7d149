<?php
declare(strict_types=1);

namespace Comave\Marketplace\Service;

use Magento\Catalog\Api\Data\ProductAttributeInterface;
use Magento\Eav\Model\Entity\Attribute\AbstractAttribute;

class SellerAttributePermission
{
    public const ATTRIBUTE_IS_SELLER_EDITABLE = 'is_seller_editable';
    public const STATUS_DISABLED = 0;
    public const STATUS_ENABLED = 1;

    /**
     * @param $attribute
     * @return bool
     */
    public static function isAttributeEditable($attribute): bool
    {
        if (!$attribute instanceof AbstractAttribute) {
            return false;
        }

        return (int) $attribute->getData(self::ATTRIBUTE_IS_SELLER_EDITABLE) !== self::STATUS_DISABLED;
    }
}
