<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Model\ResourceModel;

use Umc\Crud\Model\ResourceModel\AbstractModel;

class Trace extends AbstractModel
{
    /**
     * Initialize resource model
     *
     * @return void
     * @codeCoverageIgnore
     * //phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init('comave_integration_traceability', 'trace_id');
    }
    //phpcs: enable
}
