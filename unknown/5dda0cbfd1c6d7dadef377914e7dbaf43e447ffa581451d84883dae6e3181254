<?php

declare(strict_types=1);

namespace Comave\SellerApi\Service;

use Comave\SellerApi\Api\ConfigurableApiInterface;
use Comave\SellerApi\Api\ResultsInterface;
use Comave\SellerApi\Api\ResultsInterfaceFactory;
use Psr\Http\Client\ClientInterface;
use Psr\Http\Message\RequestFactoryInterface;

class RequestHandler
{
    /**
     * @param ClientInterface $client
     * @param RequestFactoryInterface $requestFactory
     * @param ResultsInterfaceFactory $resultsFactory
     */
    public function __construct(
        private readonly ClientInterface $client,
        private readonly RequestFactoryInterface $requestFactory,
        private readonly ResultsInterfaceFactory $resultsFactory
    ) {
    }

    /**
     * @param ConfigurableApiInterface $configurableApi
     * @return ResultsInterface
     * @throws \Psr\Http\Client\ClientExceptionInterface
     */
    public function handleRequest(ConfigurableApiInterface $configurableApi): ResultsInterface
    {
        $httpRequest = $this->requestFactory->createRequest(
            $configurableApi->getMethod(),
            $configurableApi->getEndpoint()
        );

        $params = $configurableApi->getParams();

        foreach ($configurableApi->getHeaders() as $headerName => $headerValue) {
            $httpRequest = $httpRequest->withHeader($headerName, $headerValue);
        }

        if (is_string($params) && !empty($params)) {
            $httpRequest = $httpRequest->withHeader('Content-Type', 'application/json')
                ->withBody(
                    $this->requestFactory->createStream($params)
                );
        }

        /** @var ResultsInterface $results */
        $results = $this->resultsFactory->create();
        $response = match ($configurableApi->getMethod()) {
            'GET' => $this->client->send(
                $httpRequest,
                !empty($params) && !is_string($params) ? [
                    'query' => $params,
                ] : []
            ),
            'POST' => is_string($params) ?
                $this->client->sendRequest($httpRequest) :
                $this->client->send($httpRequest, ['form_params' => $params])
        };
        $results->setResult($response)->setError(false);

        if (!in_array($response->getStatusCode(), [200, 201, 202])) {
            $results->setError(true);
        }

        return $results;
    }
}
