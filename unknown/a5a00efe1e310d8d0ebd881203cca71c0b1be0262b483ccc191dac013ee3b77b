<?php
declare(strict_types=1);

namespace Comave\DbClean\Console;

use Magento\Framework\App\State;
use Magento\Framework\App\ResourceConnection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\Framework\Filesystem\Io\File as IoFile;
use Magento\Framework\Module\Dir;
use Magento\Framework\Module\Dir\Reader as DirReader;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\Filesystem\Driver\File;
use Comave\DbClean\Service\DisabledModuleConfigProvider;
use Comave\DbClean\Service\MissingXmlSectionsProvider;
use Psr\Log\LoggerInterface;

class CleanCoreConfigSettings extends Command
{
    private const string EXPORT_SOURCE_DIR = 'export-settings';

    public function __construct(
        private readonly ResourceConnection $resource,
        private readonly State $state,
        private readonly IoFile $ioFile,
        private readonly LoggerInterface $logger,
        private readonly DirReader $dirReader,
        private readonly TimezoneInterface $timezone,
        private readonly File $fileDriver,
        private readonly DisabledModuleConfigProvider $disabledModuleConfigProvider,
        private readonly MissingXmlSectionsProvider $missingXmlSectionsProvider,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    /**
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('comave:core-config-cleaner')
            ->setDescription(
                "Clean up core_config_data table. Available options:
                --prefix-path=<path> Deletes all config entries where path starts with the given prefix.
                --cleanup-configs-for-obsolete-modules  Deletes config entries related to known obsolete modules.
                --check-missing-xml-sections Lists config prefixes in core_config_data that are not declared in any system.xml sections.
                "
            )
            ->addOption(
                'prefix-path',
                null,
                InputOption::VALUE_OPTIONAL,
                'prefix path to delete settings'
            )
            ->addOption(
                'cleanup-configs-for-obsolete-modules',
                null,
                InputOption::VALUE_NONE,
                'Deletes configs for modules that no longer exist'
            )
            ->addOption(
                'check-missing-xml-sections',
                null,
                InputOption::VALUE_NONE,
                'Finds config prefixes in the database that are not declared in any system.xml sections'
            );
        parent::configure();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->state->setAreaCode('adminhtml');

        $prefixPath = trim((string)($input->getOption('prefix-path') ?? ''));
        if ($prefixPath !== '') {
            return $this->handlePrefixDeletion($prefixPath, $output);
        }

        if ($input->getOption('cleanup-configs-for-obsolete-modules')) {
            return $this->handleObsoleteModuleCleanup($output);
        }

        if ($input->getOption('check-missing-xml-sections')) {
            return $this->handleMissingXmlCheck($output);
        }

        $output->writeln('No valid options provided. Use --prefix-path | --cleanup-configs-for-obsolete-modules or --check-missing-xml-sections');
        return Command::INVALID;
    }

    /**
     * @param string $path
     * @return int
     */
    private function deleteConfigEntriesStartingWith(string $path): int
    {
        $connection = $this->resource->getConnection();
        $tableName = $this->resource->getTableName('core_config_data');

        $exportPath = $this->getExportDirectory(self::EXPORT_SOURCE_DIR);

        try {
            $select = $connection->select()
                ->from($tableName)
                ->where('path LIKE ?', $path . '%');
            $rows = $connection->fetchAll($select);

            if (empty($rows)) {
                $this->logger->info("No config entries found for path '$path'.");
                return 0;
            }

            $sanitizedPath = preg_replace('/[^a-zA-Z0-9_-]/', '_', $path);
            $fileName = $sanitizedPath . '_' . $this->timezone->date()->getTimestamp() . '.json';

            $settings = json_encode($rows, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            $this->ioFile->write($exportPath . $fileName, $settings);
            $this->logger->info('Backed up' . count($rows) . ' settings to: ' . $exportPath.$fileName);

            $deletedRows = $connection->delete(
                $tableName,
                ['path LIKE ?' => $path . '%']
            );
            $this->logger->info(
                'Deleted entries',
                [
                    'count' => $deletedRows,
                    'path' => $path
                ]
            );

            return $deletedRows;
        } catch (\Exception $e) {
            $this->logger->error("Error deleting config entries starting with '$path': " . $e->getMessage());
            return 0;
        }
    }

    /**
     * @return string
     */
    private function getExportDirectory(string $directory): string
    {
        $exportPath = $this->dirReader->getModuleDir(
            Dir::MODULE_ETC_DIR,
            'Comave_DbClean'
            ) .'/'. $directory.'/';

        if (!$this->fileDriver->isExists($exportPath)) {
            $this->fileDriver->createDirectory($exportPath);
        }

        return $exportPath;
    }

    /**
     * @param string $prefixPath
     * @param OutputInterface $output
     * @return int
     */
    private function handlePrefixDeletion(string $prefixPath, OutputInterface $output): int
    {
        $deletedRows = $this->deleteConfigEntriesStartingWith($prefixPath);
        $output->writeln(
            $deletedRows > 0
                ? "Deleted $deletedRows entries for '$prefixPath%'"
                : "No entries to delete for '$prefixPath%'"
        );

        return Command::SUCCESS;
    }

    /**
     * @param OutputInterface $output
     * @return int
     */
    private function handleObsoleteModuleCleanup(OutputInterface $output): int
    {
        $disabledConfigPaths = $this->disabledModuleConfigProvider->getDisabledModuleConfigPathsValues();

        foreach ($disabledConfigPaths as  $disabledConfigPath) {
            $deletedRows = $this->deleteConfigEntriesStartingWith($disabledConfigPath);
            $output->writeln(
                $deletedRows > 0
                    ? "Deleted $deletedRows entries for '$disabledConfigPath%'"
                    : "No entries to delete for '$disabledConfigPath%'"
            );
        }

        return Command::SUCCESS;
    }

    /**
     * @param OutputInterface $output
     * @return int
     */
    private function handleMissingXmlCheck(OutputInterface $output): int
    {
        $missingXmlSections = $this->missingXmlSectionsProvider->getMissingXmlSections();
        if (count($missingXmlSections)) {
            $output->writeln(
                "The following path prefixes exists in core_config_data but not found the system.xml files as section:"
            );

            foreach ($missingXmlSections as $prefixPath) {
                $output->writeln(" {$prefixPath['prefix']} ({$prefixPath['configs']} entries)");
            }
        } else {
            $output->writeln("All config path prefixes from the database are declared in system.xml files.");
        }

        return Command::SUCCESS;
    }
}
