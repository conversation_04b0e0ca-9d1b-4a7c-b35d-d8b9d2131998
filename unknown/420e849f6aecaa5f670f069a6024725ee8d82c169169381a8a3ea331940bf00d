<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Service;

use Comave\Integration\Api\TraceabilityMessageInterface;
use Comave\Integration\Api\TraceabilityMessageInterfaceFactory;
use Comave\Integration\Model\Configuration\Traceability as TraceabilityConfiguration;
use Comave\Integration\Model\Queue\Consumer\TraceabilityProcessing;
use Magento\Framework\MessageQueue\PublisherInterface;

class Traceability
{
    /**
     * @param \Comave\Integration\Api\TraceabilityMessageInterfaceFactory $messageFactory
     * @param \Magento\Framework\MessageQueue\PublisherInterface $publisher
     */
    public function __construct(
        private readonly TraceabilityConfiguration $traceabilityConfig,
        private readonly TraceabilityMessageInterfaceFactory $messageFactory,
        private readonly PublisherInterface $publisher
    ) {
    }

    /**
     * @param \Comave\Integration\Api\TraceabilityMessageInterface $message
     * @return void
     */
    public function register(string $method, string $endpoint, ?string $payload): void
    {
        if ($this->traceabilityConfig->isEnabled()) {
            /**
             * @var TraceabilityMessageInterface $traceabilityMessage
             */
            $traceabilityMessage = $this->messageFactory->create();
            $traceabilityMessage->setMethod($method)->setEndpoint($endpoint)->setPayload($payload);
            $this->publisher->publish(TraceabilityProcessing::TRACEABILITY_TOPIC_NAME, $traceabilityMessage);
        }
    }
}
