<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Api;

use Comave\Integration\Api\Data\TraceInterface;

interface TraceRepositoryInterface
{
    /**
     * @param \Comave\Integration\Api\Data\TraceInterface $trace
     * @return \Comave\Integration\Api\Data\TraceInterface
     */
    public function save(TraceInterface $trace);

    /**
     * @param int $traceId
     * @return \Comave\Integration\Api\Data\TraceInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $traceId);

    /**
     * @param \Comave\Integration\Api\Data\TraceInterface $trace
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(TraceInterface $trace);

    /**
     * @param int $traceId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById(int $traceId);

    /**
     * Clear caches instances
     *
     * @return void
     */
    public function clear(): void;
}
