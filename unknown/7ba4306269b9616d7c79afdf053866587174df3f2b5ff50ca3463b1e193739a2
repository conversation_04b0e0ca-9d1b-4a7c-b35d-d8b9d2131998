### 📦 Comave Twilio Module

This covers:
- Configuration
- GraphQL Usage

Configuration is done via the admin panel under: Store > Configuration > Comave > Twi<PERSON>

### Under twilio Verfiy > Services > Create new: then copy the SID and Auth Token to the configuration.

<img width="1728" alt="image" src="https://github.com/user-attachments/assets/c02b955c-f9a2-48f0-bd79-ccef97551c9c" />
<img width="1728" alt="image" src="https://github.com/user-attachments/assets/7a111cc2-f14c-477c-8afa-7db7b2e46dde" />

### under twilio's dashboard copy the Account SID and Auth token

<img width="1727" alt="image" src="https://github.com/user-attachments/assets/d3c7b67c-db98-4f7e-b5c5-8253f2e91a4c" />

### Under comave admin panel > Store > Configuration > Comave > T<PERSON><PERSON>

Add the Account, Service SID and Auth Token from the previous steps, then save the configuration.
<img width="1728" alt="image" src="https://github.com/user-attachments/assets/17bc4051-63ad-4884-8a89-9778037ab687" />

## 🚀 Usage

### 1. Send Verification Code

**Mutation:**
```graphql
mutation SendCode($phoneNumber: String!) {
  sendPhoneVerificationCode(phoneNumber: $phoneNumber) {
    success
    message
    status
    verification_sid
  }
}
```

**Variables:**
```json
{
  "phoneNumber": "+************"
}
```

**Expected Response:**
```json
{
  "data": {
    "sendPhoneVerificationCode": {
      "success": true,
      "message": "Code sent.",
      "status": "pending",
      "verification_sid": "VE59c5dbda3cb0421e66f97582ebf5b78a"
    }
  }
}
```

---

### 2. Verify Received Code

**Mutation:**
```graphql
mutation VerifyCode($phoneNumber: String!, $verificationCode: String!) {
  verifyPhoneNumber(phoneNumber: $phoneNumber, verificationCode: $verificationCode) {
    success
    message
    status
    verification_sid
  }
}
```

**Variables:**
```json
{
  "phoneNumber": "+************",
  "verificationCode": "563241"
}
```

**Expected Response:**
```json
{
  "data": {
    "verifyPhoneNumber": {
      "success": true,
      "message": "Phone verified.",
      "status": "verified",
      "verification_sid": null
    }
  }
}
```

---

### 3. Check Verification Status

**Query:**
```graphql
query CheckStatus($phoneNumber: String!) {
  checkPhoneVerificationStatus(phoneNumber: $phoneNumber) {
    is_verified
    phone_number
    verified_at
  }
}
```

**Variables:**
```json
{
  "phoneNumber": "+************"
}
```

**Expected Response:**
```json
{
  "data": {
    "checkPhoneVerificationStatus": {
      "is_verified": true,
      "phone_number": "+************",
      "verified_at": "2025-05-30 19:38:42"
    }
  }
}
```

<img width="1107" alt="Screenshot 2025-05-30 at 20 39 41" src="https://github.com/user-attachments/assets/6c54dd80-33b1-4743-a164-a8ac1dccb851" />
<img width="1107" alt="Screenshot 2025-05-30 at 20 39 46" src="https://github.com/user-attachments/assets/7655fbd5-6021-469d-962b-11099ae0b471" />
<img width="1107" alt="Screenshot 2025-05-30 at 20 39 50" src="https://github.com/user-attachments/assets/bb5d9cb3-c88d-4fca-bf7a-814e6bc2fc64" />
