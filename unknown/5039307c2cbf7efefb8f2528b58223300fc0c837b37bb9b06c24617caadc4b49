<?php
declare(strict_types=1);

namespace Comave\Marketplace\ViewModel;

use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class AdminImpersonation implements ArgumentInterface
{
    public const CORE_IMPERSONATION_KEY = 'logged_as_customer_admind_id';

    public function __construct(
        private readonly CustomerSession $customerSession
    ) {
    }

    /**
     * Check if admin is impersonating a customer
     *
     * @return bool
     */
    public function isAdminImpersonating(): bool
    {
        return (bool) $this->customerSession->getData(self::CORE_IMPERSONATION_KEY);
    }
}
