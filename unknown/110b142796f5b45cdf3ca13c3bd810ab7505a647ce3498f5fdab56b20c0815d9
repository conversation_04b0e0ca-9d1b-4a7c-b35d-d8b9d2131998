<?php

declare(strict_types=1);

namespace Comave\Rma\Service;

use Comave\Rma\Model\ConfigProvider;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Area;
use Magento\Framework\App\AreaInterface;
use Magento\Framework\App\AreaList;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Rma\Api\Data\RmaInterface;
use Magento\Store\Model\Store;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class EmailSender
{
    /**
     * @param ConfigProvider $configProvider
     * @param StateInterface $inlineTranslation
     * @param TransportBuilder $transportBuilder
     * @param ScopeConfigInterface $scopeConfig
     * @param LoggerInterface $logger
     * @param StoreManagerInterface $storeManager
     * @param AreaList $areaList
     */
    public function __construct(
        private readonly ConfigProvider $configProvider,
        private readonly StateInterface $inlineTranslation,
        private readonly TransportBuilder $transportBuilder,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly LoggerInterface $logger,
        private readonly StoreManagerInterface $storeManager,
        private readonly AreaList $areaList
    ) {
    }

    /**
     * @param RmaInterface $rma
     * @param string|int $websiteId
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\MailException
     */
    public function sendSellerEmail(
        RmaInterface $rma,
        string|int $websiteId,
    ): void {
        if (!$this->configProvider->isEmailNotificationEnabled((int) $websiteId)) {
            return;
        }

        $emailTemplate = $this->configProvider->getEmailNotificationTemplate((int) $websiteId);

        if (empty($emailTemplate)) {
            return;
        }

        $rmaSeller = $rma->getExtensionAttributes()?->getSeller() ?? null;

        if (empty($rmaSeller)) {
            return;
        }

        $this->inlineTranslation->suspend();

        $areaObject = $this->areaList->getArea(Area::AREA_FRONTEND);
        $areaObject->load(AreaInterface::PART_TRANSLATE);

        $sender = [
            'email' => $this->scopeConfig->getValue('trans_email/ident_sales/email'),
            'name' => $this->scopeConfig->getValue('trans_email/ident_sales/name'),
        ];
        $store = $this->storeManager->getStore($rma->getStoreId());
        $templateVars = [
            'rma' => $rma,
            'subject' => 'Seller RMA Notification - A new RMA has been submitted',
            'rma_data' => [
                'status_label' => $rma->getStatusLabel(),
            ],
            'order' => $rma->getOrder(),
            'order_data' => [
                'customer_name' => $rma->getOrder()->getCustomerName(),
            ],
            'supportEmail' => $store->getConfig('trans_email/ident_support/email'),
        ];

        $transport = $this->transportBuilder
            ->setTemplateIdentifier($emailTemplate)
            ->setTemplateOptions(
                [
                    'area' => Area::AREA_FRONTEND,
                    'store' => $rma->getStoreId() ?? Store::DEFAULT_STORE_ID,
                ]
            )
            ->setTemplateVars($templateVars)
            ->setFromByScope($sender, Store::DEFAULT_STORE_ID);


        $transport->addTo($rmaSeller->getEmail());
        $transport->setReplyTo('<EMAIL>');

        $transport = $transport->getTransport();
        $transport->sendMessage();
        $this->inlineTranslation->resume();
    }
}
