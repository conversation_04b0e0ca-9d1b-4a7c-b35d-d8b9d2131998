<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Model;

use Comave\Integration\Api\Data\TraceInterface;
use Comave\Integration\Api\Data\TraceInterfaceFactory;
use Comave\Integration\Api\TraceRepositoryInterface;
use Comave\Integration\Model\ResourceModel\Trace as TraceResourceModel;
use Exception;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class TraceRepository implements TraceRepositoryInterface
{
    /**
     * @var TraceInterface[]
     */
    private array $cache = [];

    /**
     * @param TraceInterfaceFactory $factory
     * @param TraceResourceModel $resource
     */
    public function __construct(
        private readonly TraceInterfaceFactory $factory,
        private readonly TraceResourceModel $resource
    ) {
    }

    /**
     * @param \Comave\Integration\Api\Data\TraceInterface $trace
     * @return \Comave\Integration\Api\Data\TraceInterface
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    public function save(TraceInterface $trace)
    {
        try {
            $this->resource->save($trace);
        } catch (Exception $exception) {
            throw new CouldNotSaveException(
                __($exception->getMessage())
            );
        }

        return $trace;
    }

    /**
     * @param int $traceId
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function deleteById(int $traceId)
    {
        return $this->delete($this->get($traceId));
    }

    /**
     * @param \Comave\Integration\Api\Data\TraceInterface $trace
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     */
    public function delete(TraceInterface $trace)
    {
        try {
            $id = $trace->getTraceId();
            $this->resource->delete($trace);
            unset($this->cache[$id]);
        } catch (Exception $exception) {
            throw new CouldNotDeleteException(
                __($exception->getMessage())
            );
        }

        return true;
    }

    /**
     * @param int $traceId
     * @return \Comave\Integration\Api\Data\TraceInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $traceId)
    {
        if (!isset($this->cache[$traceId])) {
            $trace = $this->factory->create();
            $this->resource->load($trace, $traceId);
            if (!$trace->getId()) {
                throw new NoSuchEntityException(
                    __('The Trace with the ID "%1" does not exist . ', $traceId)
                );
            }
            $this->cache[$traceId] = $trace;
        }

        return $this->cache[$traceId];
    }

    /**
     * @inheritDoc
     */
    public function clear(): void
    {
        $this->cache = [];
    }
}
