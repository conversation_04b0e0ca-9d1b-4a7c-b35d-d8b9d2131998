<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
	<system>
		<section id="comave_integration" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
			<label>REST API Integration</label>
			<tab>comave</tab>
            <resource>Comave_Integration::config</resource>
			<group id="invalidate_jwt_tokens" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
				<label>Jwt Token Settings</label>
				<field id="enable" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
					<label>Invalidate Old auth tokens</label>
					<comment/>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Invalidate Old Jwt auth tokens after a new one is generated in REST API (admin and customer resources).]]></comment>
				</field>
			</group>
			<group id="traceability" sortOrder="20" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
				<label>Traceability</label>
				<field id="enable" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
					<label>Enable</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment><![CDATA[Enable Traceability for REST APIs.]]></comment>
				</field>
			</group>
		</section>
	</system>
</config>
