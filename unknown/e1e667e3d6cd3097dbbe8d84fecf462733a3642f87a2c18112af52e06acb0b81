<?php
declare(strict_types=1);

namespace Comave\Twilio\Model;

use Magento\Framework\Model\AbstractModel;
use Comave\Twilio\Api\Data\VerificationInterface;

class Verification extends AbstractModel implements VerificationInterface
{
    protected function _construct(): void
    {
        $this->_init(\Comave\Twilio\Model\ResourceModel\Verification::class);
    }

    public function getId(): ?int
    {
        $id = $this->getData('verification_id');
        return $id !== null ? (int)$id : null;
    }

    public function getCustomerId(): ?int
    {
        $customerId = $this->getData('customer_id');
        return $customerId !== null ? (int)$customerId : null;
    }

    public function getPhoneNumber(): string
    {
        return (string)$this->getData('phone_number');
    }

    public function getVerificationSid(): string
    {
        return (string)$this->getData('verification_sid');
    }

    public function getStatus(): string
    {
        return (string)$this->getData('status');
    }

    public function getVerifiedAt(): ?string
    {
        return $this->getData('verified_at');
    }

    public function getCreatedAt(): string
    {
        return (string)$this->getData('created_at');
    }

    public function setCustomerId(int $id): self
    {
        return $this->setData('customer_id', $id);
    }

    public function setPhoneNumber(string $phoneNumber): self
    {
        return $this->setData('phone_number', $phoneNumber);
    }

    public function setVerificationSid(string $sid): self
    {
        return $this->setData('verification_sid', $sid);
    }

    public function setStatus(string $status): self
    {
        return $this->setData('status', $status);
    }

    public function setVerifiedAt(?string $verifiedAt): self
    {
        return $this->setData('verified_at', $verifiedAt);
    }

    public function isVerified(): bool
    {
        return $this->getStatus() === self::STATUS_VERIFIED;
    }
}
