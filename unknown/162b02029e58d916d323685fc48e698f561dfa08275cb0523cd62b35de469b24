<?php /** @var $block \Magento\Framework\View\Element\Template */ ?>
<?php /** @var $adminImpersonation \Comave\Marketplace\ViewModel\AdminImpersonation */ ?>
<?php /** @var $escaper \Magento\Framework\Escaper */ ?>
<?php

$buttonState = $block->getDate('button_state');
$label = $block->getData('label');
$url = $block->getData('url');
$type = $block->getData('type') ?: 'button';
$cssClass = $block->getData('css_class');

?>

<div class="<?= $escaper->escapeHtmlAttr($cssClass) ?>">
    <?php if ($buttonState === 'disabled'): ?>
        <button type="button" disabled><?= $escaper->escapeHtml(__($label)) ?></button>
    <?php else: ?>
        <a href="<?= $escaper->escapeUrl($url) ?>">
            <button type="<?= $escaper->escapeHtmlAttr($type) ?>"><?= $escaper->escapeHtml(__($label)) ?></button>
        </a>
    <?php endif; ?>
</div>
