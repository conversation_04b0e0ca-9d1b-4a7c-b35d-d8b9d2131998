<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">

    <table name="comave_phone_verification"
           resource="default"
           engine="innodb"
           comment="Phone verification">

        <column name="verification_id" xsi:type="int" unsigned="true" identity="true" nullable="false"/>
        <column name="customer_id" xsi:type="int" nullable="false"/>
        <column name="phone_number" xsi:type="varchar" length="32" nullable="false"/>
        <column name="verification_sid" xsi:type="varchar" length="48" nullable="false"/>
        <column name="status" xsi:type="varchar" length="16" nullable="false"/>
        <column name="verified_at" xsi:type="timestamp" nullable="true" on_update="false"/>
        <column name="created_at" xsi:type="timestamp" nullable="false" default="CURRENT_TIMESTAMP"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="verification_id"/>
        </constraint>

        <index referenceId="COMAVE_PHONE" indexType="btree">
            <column name="phone_number"/>
        </index>

    </table>
</schema>
