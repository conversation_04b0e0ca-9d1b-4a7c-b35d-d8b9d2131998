<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Plugin;

use Comave\Integration\Service\Traceability;
use Comave\SellerApi\Api\ConfigurableApiInterface;
use Comave\SellerApi\Service\RequestHandler;

class SellerApiRequestHandlerPlugin
{
    /**
     * @param \Comave\Integration\Service\Traceability $traceabilityService
     */
    public function __construct(
        private readonly Traceability $traceabilityService,
    ) {
    }

    /**
     * @param \Comave\SellerApi\Service\RequestHandler $subject
     * @param \Comave\SellerApi\Api\ConfigurableApiInterface $configurableApi
     * @return array
     */
    public function beforeHandleRequest(
        RequestHandler $subject,
        ConfigurableApiInterface $configurableApi
    ): array {
        $a = 1;
        $this->traceabilityService->register(
            method: $configurableApi->getMethod(),
            endpoint: $configurableApi->getEndpoint(),
            payload: $configurableApi->getParams(),
        );

        return [$configurableApi];
    }
}