<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Webhook\Processors;

use Comave\ShopifyAccounts\Api\WebhookTopicProcessorInterface;
use Comave\ShopifyAccounts\Api\WebhookValidatorInterface;
use Comave\ShopifyAccounts\Exception\InvalidWebhookRequestException;
use Magento\Framework\App\ResourceConnection;
use Webkul\MpMultiShopifyStoreMageConnect\Model\OrdermapFactory;
use Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Ordermap;
use Comave\ShopifyOrderSync\Helper\Shopify as ShopifyHelper;
use Magento\Framework\App\HttpRequestInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;
use Comave\MapOrderStatuses\Service\OrderStatuses;
use Comave\MapOrderStatuses\Model\ConfigProvider;

class Order implements WebhookTopicProcessorInterface
{
    /**
     * @param OrdermapFactory $orderFactory
     * @param Ordermap $resourceModel
     * @param LoggerInterface $logger
     * @param OrderRepositoryInterface $orderRepository
     * @param ShopifyHelper $shopifyHelper
     * @param OrderStatuses $orderStatuses
     * @param ConfigProvider $orderStatusConfigProvider
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        private readonly OrdermapFactory $orderFactory,
        private readonly Ordermap $resourceModel,
        private readonly LoggerInterface $logger,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly ShopifyHelper $shopifyHelper,
        private readonly OrderStatuses $orderStatuses,
        private readonly ConfigProvider $orderStatusConfigProvider,
        private readonly ResourceConnection $resourceConnection,
    ) {
    }

    /**
     * @param HttpRequestInterface $request
     * @return void
     * @throws InvalidWebhookRequestException
     */
    public function process(HttpRequestInterface $request): void
    {
        /** @var string|null $jsonParam */
        $jsonParam = $request->getContent();

        $this->logger->info(
            '[WebhookProcessor] Beginning order sync',
            [
                'body' => $jsonParam
            ]
        );

        $shopifyOrderId = $request->getHeader(
            WebhookValidatorInterface::ORDER_ID_HEADER,
            ''
        );

        if (!$shopifyOrderId) {
            $this->logger->warning(
                '[WebhookProcessor] Missing order ID header',
            );

            throw new InvalidWebhookRequestException(
                __('Order ID not found')
            );
        }

        $shopifyOrder = $this->orderFactory->create();
        $this->resourceModel->load($shopifyOrder, $shopifyOrderId, 'shopify_order_id');

        if (!$shopifyOrder->getId()) {
            $this->logger->warning(
                '[WebhookProcessor] Unidentified order ID provided',
                [
                    'shopifyOrderId' => $shopifyOrderId
                ]
            );

            throw new InvalidWebhookRequestException(
                __('Unable to identify shopify order')
            );
        }

        $decoded = json_decode($jsonParam, true, JSON_THROW_ON_ERROR);
        $shopId = $shopifyOrder->getRuleId();
        $shopifyOrderStatus = $decoded['status'] ??
            $this->shopifyHelper->getOrderStatus($shopifyOrderId, $shopId);

        if ($this->orderStatusConfigProvider->isCustomStatusMapped($shopifyOrderStatus) === false) {
            $this->logger->warning(
                'Skipped status update: Status is inactive or not permitted.',
                [
                    'order_id' => $shopifyOrderId,
                    'status' => $shopifyOrderStatus,
                ]
            );
            throw new InvalidWebhookRequestException(
                __('Status is inactive or not allowed.')
            );
        }

        $mappedStatus = $this->orderStatusConfigProvider->getStatus(
            ConfigProvider::SHOPIFY_PROVIDER,
            $shopifyOrderStatus
        );

        if (!$this->orderStatuses->isStatusActive($mappedStatus)) {
            $this->logger->warning(
                'Skipped status update: Status is not mapped or not permitted.',
                [
                    'order_id' => $shopifyOrderId,
                    'status' => $shopifyOrderStatus,
                    'magento_status' => $mappedStatus
                ]
            );
            throw new InvalidWebhookRequestException(
                __('Status is not mapped or not allowed.')
            );
        }

        //@todo - we are pushing draft orders which is not the final data for shopify orders, no tracking is available on draft orders
//        $trackingData = $this->shopifyHelper->getTrackingDetails($shopifyOrderId, $shopId);
// Update tracking details if available
//        if (!empty($trackingData[0]['tracking_numbers'])) {
//            $trackingNumbers = $trackingData[0]['tracking_numbers'];
//            $trackingCompany = $trackingData[0]['tracking_company'];
//            $this->shopifyHelper->updateMagentoOrderTracking($orderID, $trackingNumbers, $trackingCompany);
//        }
        $magentoOrderId = $shopifyOrder->getMageOrderId();
        $magentoOrder = $this->orderRepository->get((int) $magentoOrderId);
        $magentoOrder->addCommentToStatusHistory(
            sprintf(
                'Successfully updated magento/shopify order %s, old status "%s", new status "%s"',
                $shopifyOrderId,
                $magentoOrder->getStatus(),
                $shopifyOrderStatus,
            )
        );
        $magentoOrder->setStatus($mappedStatus)
            ->setState($this->getStateByStatus($mappedStatus));
        $this->orderRepository->save($magentoOrder);

        // Log success message
        $this->logger->info(
            '[WebhookProcessor] Successfully updated magento/shopify order',
            [
                'processor' => __CLASS__,
                'magentoId' => $magentoOrderId,
                'shopifyId' => $shopifyOrderId
            ]
        );
    }

    /**
     * @param string $status
     * @return string
     */
    private function getStateByStatus(string $status): string
    {
        $connection = $this->resourceConnection->getConnection('read');
        $selectState = $connection->select()
            ->from(
                ['main' => $connection->getTableName('sales_order_status_state')],
                [
                    'state'
                ]
            )->where(
                'status = ?',
                $status
            );

        return $connection->fetchOne($selectState);
    }
}
