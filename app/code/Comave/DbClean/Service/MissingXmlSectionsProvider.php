<?php
declare(strict_types=1);

namespace Comave\DbClean\Service;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Filesystem\Io\File as IoFile;
use Magento\Framework\Module\Dir\Reader as ModuleDir;
use Magento\Framework\Module\ModuleListInterface;

class  MissingXmlSectionsProvider
{
    public function __construct(
        private readonly ResourceConnection $resource,
        private readonly ModuleListInterface $moduleList,
        private readonly ModuleDir $moduleDir,
        private readonly IoFile $ioFile,
    ) {}

    /**
     * @return array
     */
    public function getMissingXmlSections(): array
    {
        $connection = $this->resource->getConnection();
        $table = $this->resource->getTableName('core_config_data');
        $rows = $connection->fetchAll("
            SELECT
                DISTINCT SUBSTRING_INDEX(path, '/', 1) AS `prefix`,
                COUNT(config_id) AS `configs`
            FROM {$table}
            GROUP BY prefix
            ORDER BY prefix ASC
        ");

        $modulesInAppCode = $this->getModulesFromAppCode();
        foreach ($modulesInAppCode as $module) {
            $path = $this->moduleDir->getModuleDir('etc', $module) . '/adminhtml/system.xml';
            if(!$this->ioFile->fileExists($path)) {
                continue;
            }

            $xml = simplexml_load_file($path);
            if (!isset($xml->system->section)) {
                continue;
            }

            foreach ($xml->system->section as $section) {
                $key = array_search($section['id'], $rows);
                if ($key !== false) {
                    unset($rows[$key]);
                }
            }
        }

        return $rows;
    }

    /**
     * @return array
     */
    private function getModulesFromAppCode(): array
    {
        $modulesInAppCode = [];
        $allModules = $this->moduleList->getAll();

        foreach ($allModules as $moduleName => $moduleData) {
            $path = $this->moduleDir->getModuleDir('', $moduleName);
            if (strpos($path, 'app/code/') !== false) {
                $modulesInAppCode[] = $moduleName;
            }
        }

        return $modulesInAppCode;
    }
}
