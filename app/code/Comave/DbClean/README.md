# Magento 2 Core Config Cleaner
## Module: Comave_DbClean
A Magento 2 module that provides a flexible CLI tool to clean up unused entries in the `core_config_data` table. 
This is useful for removing obsolete settings left behind by removed modules or unused configuration paths.

## Features:
- bin/magento comave:core-config-cleaner
This Magento 2 console command helps clean up entries in the `core_config_data` table that are no longer used, such as:
| Option                                     | Description                                                                 |
|--------------------------------------------|-----------------------------------------------------------------------------|
| `--prefix-path=<path>`                     | Deletes all `core_config_data` entries where the `path` starts with this prefix. |
| `--cleanup-configs-for-obsolete-modules`   | Deletes configuration entries related to known obsolete/removed modules.   |
| `--check-missing-xml-sections`             | Lists path prefixes from the DB that are not declared in any `system.xml`. |
