<?php
declare(strict_types=1);

namespace Comave\ShopifyOrderSync\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Framework\Json\Helper\Data as JsonHelper;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Sales\Api\ShipmentRepositoryInterface;
use Magento\Sales\Api\ShipmentTrackRepositoryInterface;
use Magento\Sales\Model\Order\Shipment\TrackFactory;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\OrderFactory;
use Comave\ShopifyOrderSync\Model\ShopifyOrderFactory;
use Webkul\Marketplace\Model\SaleslistFactory;
use Webkul\MpMultiShopifyStoreMageConnect\Helper\Data as MpHelper;
use Webkul\Marketplace\Model\OrdersFactory as MpOrdersFactory;
use Magento\Framework\HTTP\Client\Curl;
use \Webkul\MpMultiShopifyStoreMageConnect\Model\ShopifyaccountsFactory;
use Webkul\MpMultiShopifyStoreMageConnect\Api\ShopifyaccountsRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;

class Shopify extends AbstractHelper
{
    protected $_storeManager;
    protected $scopeConfig;
    protected $encryptor;
    protected $jsonHelper;
    protected $orderFactory;
    protected $productRepository;
    protected $customerRepository;
    protected $shopifyOrderFactory;
    protected $orderRepository;
    protected $helper;
    protected $mpOrdersFactory;
    protected $saleslistFactory;
    protected $_curl;
    protected $shopifyAccountsRepository;
    public $_shopifyaccountsFactory;
    protected $shipmentRepository;
    protected $trackRepository;
    protected $trackFactory;
    protected $orderinterface;
    protected $searchCriteriaBuilder;

    const SHOPIFY_API_VERSION = '2024-04';

    public function __construct(
        Context $context,
        ShopifyaccountsFactory $shopifyaccountsFactory,
        ShopifyaccountsRepositoryInterface $shopifyAccountsRepository,
        StoreManagerInterface $storeManager,
        ScopeConfigInterface $scopeConfig,
        EncryptorInterface $encryptor,
        JsonHelper $jsonHelper,
        Curl $curl,
        OrderFactory $orderFactory,
        SaleslistFactory $saleslistFactory = null,
        ProductRepositoryInterface $productRepository,
        CustomerRepositoryInterface $customerRepository,
        OrderRepositoryInterface $orderRepository,
        ShopifyOrderFactory $shopifyOrderFactory,
        MpHelper $helper,
        MpOrdersFactory $mpOrdersFactory,
        ShipmentRepositoryInterface $shipmentRepository,
        ShipmentTrackRepositoryInterface $trackRepository,
        TrackFactory $trackFactory,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        \Magento\Sales\Api\Data\OrderInterface $orderinterface
    ) {
        parent::__construct($context);
        $this->_storeManager = $storeManager;
        $this->orderinterface = $orderinterface;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->scopeConfig = $scopeConfig;
        $this->encryptor = $encryptor;
         $this->orderFactory = $orderFactory;
        $this->jsonHelper = $jsonHelper;
        $this->productRepository = $productRepository;
        $this->customerRepository = $customerRepository;
        $this->orderRepository = $orderRepository;
        $this->helper = $helper;
        $this->_shopifyaccountsFactory = $shopifyaccountsFactory;
        $this->shopifyAccountsRepository = $shopifyAccountsRepository;
        $this->_curl = $curl;
        $this->mpOrdersFactory = $mpOrdersFactory;
        $this->shopifyOrderFactory = $shopifyOrderFactory;
        $this->saleslistFactory = $saleslistFactory ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(SaleslistFactory::class);
        $this->shipmentRepository = $shipmentRepository;
        $this->trackRepository = $trackRepository;
        $this->trackFactory = $trackFactory;
    }

    public function isEnabled(): bool
    {
        $storeId = $this->_storeManager->getStore()->getId();
        return (bool)$this->scopeConfig->getValue(
            'mpmultishopifystoremageconnect/general_settings/enable',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
    public function enabledWebhook()
    {
        $storeId = $this->_storeManager->getStore()->getId();
        return $this->scopeConfig->getValue(
            'mpmultishopifystoremageconnect/general_settings/enable_webhook',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function createShopifyOrder($shopId, $products, Order $order)
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/SyncOrderAtShopify.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        try {
            $shopifyCredentials = $this->helper->getTheShopApiCredentials($shopId, true);

            $shopifyApiUrl = 'https://' . $shopifyCredentials['shopify_domain_name'] . '/admin/api/' . self::SHOPIFY_API_VERSION . '/orders.json';
            $orderData = $this->prepareOrderData($shopId, $products, $order);

            $logger->info('Order Data');
            if(!empty($orderData)){

                $logger->info(print_r($orderData,true));

                $curl = curl_init();

                curl_setopt_array($curl, [
                    CURLOPT_URL => $shopifyApiUrl,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => json_encode($orderData),
                    CURLOPT_HTTPHEADER => [
                        'X-Shopify-Access-Token: ' . $shopifyCredentials['access_token'],
                        'Content-Type: application/json'
                    ],
                ]);

                $response = curl_exec($curl);

                if (curl_errno($curl)) {
                    $logger->error('cURL error: ' . curl_error($curl));
                    throw new \Exception('Error occurred during the cURL request.');
                }

                $httpStatusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                curl_close($curl);

                $logger->info('HTTP Status Code: ' . $httpStatusCode);

                if ($httpStatusCode === 201) {
                    $products = json_decode($response, true);
                    return $products;
                } else {
                    return null;
                }
            }

        } catch (\Exception $e) {
            $logger->error('Error creating Shopify order: ' . $e->getMessage());
            throw $e;
        }
    }

    protected function prepareOrderData($shopId, $products, Order $order): array
    {
        // Initialize logger
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/SyncOrderAtShopify.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        $logger->info('Preparing order data for Shopify.');

        $lineItems = [];
        $totalprice = 0;
        $total_tax = 0;

        foreach ($products as $product) {
            $collection = $this->saleslistFactory->create()
                ->getCollection()
                ->addFieldToFilter('seller_id', $product['seller_ID'])
                ->addFieldToFilter('order_id', $order->getId());

            foreach ($collection as $orderData) {
                $total_tax += $orderData->getTotalAmount();
            }

            foreach ($order->getAllItems() as $orderItem) {
                if ($orderItem->getProductId() == $product['product_id']) {
                    if(!empty($product['variant_id'])){
                        $totalprice += $orderItem->getPrice();
                        $lineItems[] = [
                            'variant_id' => $product['variant_id'],
                            'title' => $orderItem->getName(),
                            'price' => $orderItem->getPrice(),
                            'quantity' => (int)$orderItem->getQtyOrdered(),
                            'tax_lines' => [
                                [
                                    'price' => $orderItem->getTaxAmount(),
                                    'rate' => $orderItem->getTaxPercent() / 100,
                                    'title' => 'Tax'
                                ]
                            ]
                        ];
                        break;
                    }
                }
            }
        }

        $transactions = [
            [
                'kind' => 'sale',
                'status' => 'success',
                'amount' => $totalprice
            ]
        ];

        $shippingAddress = $order->getShippingAddress();
        $billingAddress = $order->getBillingAddress();

        $storeId = $this->_storeManager->getStore()->getId();
        $supportEmail = $this->scopeConfig->getValue('trans_email/ident_support/email', \Magento\Store\Model\ScopeInterface::SCOPE_STORE, $storeId);
        $supportPhone = $this->scopeConfig->getValue('general/store_information/phone', \Magento\Store\Model\ScopeInterface::SCOPE_STORE, $storeId);

        $tags = ['Comave']; // Modify as needed for additional tags

        if (!empty($lineItems)) {
            return [
                'order' => [
                    'line_items' => $lineItems,
                    'transactions' => $transactions,
                    'total_tax' => $total_tax,
                    'currency' => $order->getOrderCurrencyCode(),
                    'customer' => [
                        'first_name' => $billingAddress->getFirstname(),
                        'last_name' => $billingAddress->getLastname(),
                        'phone' => $supportPhone,
                        'email' => $supportEmail,
                    ],
                    'shipping_address' => [
                        'first_name' => $shippingAddress->getFirstname(),
                        'last_name' => $shippingAddress->getLastname(),
                        'address1' => $shippingAddress->getStreetLine(1),
                        'address2' => $shippingAddress->getStreetLine(2),
                        'city' => $shippingAddress->getCity(),
                        'province' => $shippingAddress->getRegion(),
                        'country' => $shippingAddress->getCountryId(),
                        'zip' => $shippingAddress->getPostcode(),
                        'phone' => $supportPhone,
                    ],
                    'billing_address' => [
                        'first_name' => $billingAddress->getFirstname(),
                        'last_name' => $billingAddress->getLastname(),
                        'address1' => $billingAddress->getStreetLine(1),
                        'address2' => $billingAddress->getStreetLine(2),
                        'city' => $billingAddress->getCity(),
                        'province' => $billingAddress->getRegion(),
                        'country' => $billingAddress->getCountryId(),
                        'zip' => $billingAddress->getPostcode(),
                        'phone' => $supportPhone,
                    ],
                    'tags' => implode(', ', $tags), // Combine tags into a comma-separated string
                    'note_attributes' => [
                        [
                            'name' => 'Magento Order ID',
                            'value' => $order->getIncrementId()
                        ]
                    ]
                ]
            ];
        } else {
            return [];
        }
    }

    public function syncOrderToShopify($shopId, $products, Order $order)
    {
        $logger = $this->logger();
        try {
            // Step 1: Create the order in Shopify
            $shopifyOrder = $this->createShopifyOrder($shopId, $products, $order);
            if(!empty($shopifyOrder)){
                $shopifyOrderId = $shopifyOrder['order']['id'];
                $this->saveOrderData($shopifyOrderId, $shopId, $order);
                $logger->info('Order Has Been Created On Shopify And The Order ID Is:- '.$shopifyOrderId);
            } else {
                $logger->info('Shopify order Not synced.');
            }
        } catch (\Exception $e) {
            $logger->err('Error syncing order to Shopify: ' . $e->getMessage());
        }
    }

    public function saveOrderData($shopifyOrderId, $shopId, $order)
    {
        $logger = $this->logger();
        $shopifySellerID = $this->getTheShopApiCredentials($shopId, true);
        $orderID = $order->getIncrementId();

        $Data = [
            'shopify_order_id' => $shopifyOrderId,
            'shop_id' => $shopId,
            'magento_order_id' => $orderID,
            'shopify_seller_id' => $shopifySellerID,
            'order_status' => 1,
        ];

        try {
            $saveOrder = $this->shopifyOrderFactory->create()->setData($Data);
            $saveOrder->save(); // This line ensures the data is saved to the database
            $logger->info('Order data saved successfully.', $Data);
        } catch (\Exception $e) {
            $logger->err('Error saving order data: ' . $e->getMessage());
        }
    }


    public function getTheShopApiCredentials($id = "", $isDecrypt = false)
    {
        $shopifyAccountsModel = $this->_shopifyaccountsFactory->create();
        $shopifyAccountsModel->load($id);
        $sellerID = $shopifyAccountsModel->getSellerId();
        return $sellerID;
    }

    public function getOrderStatus($shopifyOrderId, $shopId)
    {
        $shopifyCredentials = $this->helper->getTheShopApiCredentials($shopId, true);

        $url = 'https://' . $shopifyCredentials['shopify_domain_name'] . "/admin/api/2023-04/draft_orders/{$shopifyOrderId}.json";
        $headers = [
            "X-Shopify-Access-Token: " . $shopifyCredentials['access_token'],
            "Content-Type: application/json"
        ];

        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => $headers,
        ]);

        $response = curl_exec($curl);
        curl_close($curl);

        $response = json_decode($response, true);
        if (isset($response['draft_order'])) {
            return $response['draft_order']['status'];
        }

        return null;
    }

    public function getTrackingDetails($shopifyOrderId, $shopId)
    {
        $logger = $this->UpdateShopifyOrderLogger();
        $shopifyCredentials = $this->helper->getTheShopApiCredentials($shopId, true);
        $url = 'https://' . $shopifyCredentials['shopify_domain_name'] . "/admin/api/2023-04/orders/{$shopifyOrderId}/fulfillments.json";
        $headers = [
            "X-Shopify-Access-Token: " . $shopifyCredentials['access_token'],
            "Content-Type: application/json"
        ];
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => $headers,
        ]);

        $response = curl_exec($curl);
        curl_close($curl);

        $response = json_decode($response, true);
        if (isset($response['fulfillments']) && count($response['fulfillments']) > 0) {
            return $response['fulfillments'];
        }

        return null;
    }

    public function getOrderIdByIncrementId($incrementId)
    {
        $logger = $this->UpdateShopifyOrderLogger();
        try {
            // Create search criteria with a filter for the increment ID
            $searchCriteria = $this->searchCriteriaBuilder
                ->addFilter('increment_id', $incrementId, 'eq')
                ->create();

            // Fetch the order list
            $orderList = $this->orderRepository->getList($searchCriteria);

            if ($orderList->getTotalCount() > 0) {
                $orders = $orderList->getItems();
                $order = reset($orders);
                $orderId = $order->getEntityId();
                return $orderId;
            } else {
                $logger->info('No order found with Increment ID: ' . $incrementId);
                return null;
            }
        } catch (NoSuchEntityException $e) {
            $logger->info('No order found with Increment ID: ' . $incrementId);
            return null;
        } catch (\Exception $e) {
            $logger->err('Error retrieving order: ' . $e->getMessage());
            return null;
        }
    }

    public function updateMagentoOrderTracking($orderID, $trackingNumbers, $trackingCompany)
    {
        $logger = $this->UpdateShopifyOrderLogger();
        try {
            // Load the order by its ID
            $order = $this->orderFactory->create()->load($orderID);
            $shipmentCollection = $order->getShipmentsCollection();

            if ($shipmentCollection->getSize()) {
                foreach ($shipmentCollection as $shipment) {
                    $existingTracks = $shipment->getAllTracks();
                    foreach ($trackingNumbers as $trackingNumber) {
                        $trackExists = false;
                        foreach ($existingTracks as $existingTrack) {
                            if ($existingTrack->getTrackNumber() == $trackingNumber) {
                                $trackExists = true;
                                break;
                            }
                        }

                        if (!$trackExists) {
                            $track = $this->trackFactory->create();
                            $track->setCarrierCode('custom'); // Set your carrier code
                            $track->setTitle($trackingCompany); // Set your carrier title
                            $track->setTrackNumber($trackingNumber);

                            $shipment->addTrack($track);
                        }
                    }
                    $this->shipmentRepository->save($shipment);
                }
                $logger->info('Order update on Magento for Increment ID: ' . $orderID);
            } else {
                throw new \Magento\Framework\Exception\LocalizedException(__('No shipments found for order Increment ID "%1".', $orderID));
            }
        } catch (\Magento\Framework\Exception\NoSuchEntityException $e) {
            $logger->err('Order with Increment ID ' . $orderID . ' does not exist. ' . $e->getMessage());
        } catch (\Magento\Framework\Exception\LocalizedException $e) {
            $logger->err('Error updating order Increment ID ' . $orderID . ': ' . $e->getMessage());
        } catch (\Exception $e) {
            $logger->err('Unexpected error updating order Increment ID ' . $orderID . ': ' . $e->getMessage());
        }
    }

     /**
      * GetAccountDetailsByRuleId function
      *
      * @return array
      */
    public function getAccBySeller($sellerId): array
    {
          return $this->_shopifyaccountsFactory->create()
                  ->getCollection()
                  ->addFieldToFilter("seller_id", ["eq"=>$sellerId])
                  ->getFirstItem()->getData();
    }


    /**
     * GetShopifyConfiguration function get the shopify account configuration
     *
     * @param string $id
     * @return void
     */
    public function getShopifyConfiguration($id = "")
    {
        return $this->shopifyAccountsRepository->getConfigurationById($id)->toArray();
    }


    /**
     * Get mp seller id
     *
     * @param int $shopId
     * @return int
     */
    public function getMpSellerId($shopId = "")
    {
        return $this->getShopifyConfiguration($shopId)['seller_id'];
    }


    public function logger(){
        // Initialize logger
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/SyncOrderAtShopify.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        return $logger;
    }

    public function UpdateShopifyOrderLogger(){
        // Initialize logger
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/UpdateShopifyOrder.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        return $logger;
    }
}
