<?php
declare(strict_types=1);

namespace Comave\Twilio\Api\Data;

interface VerificationInterface
{
    public const STATUS_PENDING  = 'pending';
    public const STATUS_VERIFIED = 'verified';
    public const STATUS_FAILED   = 'failed';
    public function getId(): ?int;
    public function getCustomerId(): ?int;
    public function getPhoneNumber(): string;
    public function getVerificationSid(): string;
    public function getStatus(): string;
    public function getVerifiedAt(): ?string;
    public function getCreatedAt(): string;
    public function setCustomerId(int $id): self;
    public function setPhoneNumber(string $phone): self;
    public function setVerificationSid(string $sid): self;
    public function setStatus(string $status): self;
    public function setVerifiedAt(?string $ts): self;
}
