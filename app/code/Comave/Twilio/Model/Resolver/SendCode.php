<?php
declare(strict_types=1);

namespace Comave\Twilio\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Comave\Twilio\Model\PhoneVerification;

class SendCode implements ResolverInterface
{
    public function __construct(private PhoneVerification $service) {}

    public function resolve(
        Field $field,
              $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (empty($args['phoneNumber'])) {
            throw new \InvalidArgumentException(__('Phone number is required.')->render());
        }

        $verificationResult = $this->service->sendVerificationCode(
            $context->getUserId() ?? 0,
            $args['phoneNumber']
        );

        return [
            'success'          => true,
            'message'          => __('Code sent.')->render(),
            'status'           => $verificationResult->getStatus(),
            'verification_sid' => $verificationResult->getVerificationSid(),
        ];
    }
}
