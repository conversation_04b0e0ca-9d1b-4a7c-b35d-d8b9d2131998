<?php
declare(strict_types=1);

namespace Comave\Twilio\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Comave\Twilio\Model\PhoneVerification;

class CheckStatus implements ResolverInterface
{
    public function __construct(private PhoneVerification $service) {}

    public function resolve(
        Field $field,
              $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        $row = $this->service->getStatus($args['phoneNumber']);

        return [
            'is_verified'  => $row->isVerified(),
            'phone_number' => $row->getPhoneNumber(),
            'verified_at'  => $row->getVerifiedAt(),
        ];
    }
}
