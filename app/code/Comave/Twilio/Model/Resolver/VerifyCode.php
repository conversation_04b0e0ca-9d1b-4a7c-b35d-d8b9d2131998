<?php
declare(strict_types=1);

namespace Comave\Twilio\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Comave\Twilio\Model\PhoneVerification;

class VerifyCode implements ResolverInterface
{
    public function __construct(private PhoneVerification $service) {}

    public function resolve(
        Field $field,
              $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (empty($args['phoneNumber']) || empty($args['verificationCode'])) {
            throw new \InvalidArgumentException(__('Phone number and verification code are required.')->render());
        }

        $verificationResult = $this->service->verifyCode(
            $args['phoneNumber'],
            $args['verificationCode']
        );

        return [
            'success' => $verificationResult->isVerified(),
            'message' => $verificationResult->isVerified()
                ? __('Phone verified.')->render()
                : __('Verification failed.')->render(),
            'status'  => $verificationResult->getStatus(),
        ];
    }
}
