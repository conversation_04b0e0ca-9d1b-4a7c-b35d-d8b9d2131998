<?php
declare(strict_types=1);

namespace Comave\Twilio\Model\ResourceModel\Verification;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{
    protected function _construct(): void
    {
        $this->_init(
            \Comave\Twilio\Model\Verification::class,
            \Comave\Twilio\Model\ResourceModel\Verification::class
        );
    }
}
