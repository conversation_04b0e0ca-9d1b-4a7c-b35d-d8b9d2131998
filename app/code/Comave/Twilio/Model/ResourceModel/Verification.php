<?php
declare(strict_types=1);

namespace Comave\Twilio\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\AbstractDb;
use Magento\Framework\Model\AbstractModel;

class Verification extends AbstractDb
{
    protected function _construct(): void
    {
        $this->_init('comave_phone_verification', 'verification_id');
    }

    public function getLatestByPhone(string $phone): AbstractModel
    {
        $connection = $this->getConnection();
        $select = $connection->select()
            ->from($this->getMainTable())
            ->where('phone_number = ?', $phone)
            ->order('verification_id DESC')
            ->limit(1);

        $data = $connection->fetchRow($select) ?: [];
        /** @var \Comave\Twilio\Model\Verification $model */
        $model = $this->getEmptyModel();
        if ($data) {
            $this->load($model, $data['verification_id']);
        }
        return $model;
    }

    private function getEmptyModel(): AbstractModel
    {
        return \Magento\Framework\App\ObjectManager::getInstance()
            ->create(\Comave\Twilio\Model\Verification::class);
    }
}
