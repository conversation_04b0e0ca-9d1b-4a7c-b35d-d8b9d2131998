type Mutation {
  sendPhoneVerificationCode(phoneNumber: String!): VerificationResponse
    @resolver(class: "Comave\\Twilio\\Model\\Resolver\\SendCode")

  verifyPhoneNumber(
    phoneNumber: String!
    verificationCode: String!
  ): VerificationResponse
    @resolver(class: "Comave\\Twilio\\Model\\Resolver\\VerifyCode")
}

type Query {
  checkPhoneVerificationStatus(phoneNumber: String!): VerificationStatus
    @resolver(class: "Comave\\Twilio\\Model\\Resolver\\CheckStatus")
}

type VerificationResponse {
  success: Boolean!
  message: String
  status: String
  verification_sid: String
}

type VerificationStatus {
  is_verified: Boolean!
  phone_number: String!
  verified_at: String
}
