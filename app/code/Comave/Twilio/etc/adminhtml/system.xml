<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="twilio" translate="label" sortOrder="10"
                 showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Twilio</label>
            <tab>comave</tab>
            <resource>Comave_Twilio::config_twilio</resource>

            <group id="general" translate="label" type="text" sortOrder="10"
                   showInDefault="1" showInWebsite="1" showInStore="0">
                <label>General Settings</label>

                <field id="account_sid" translate="label" type="text" sortOrder="10"
                       showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Account SID</label>
                    <validate>required-entry</validate>
                </field>

                <field id="auth_token" translate="label" type="obscure" sortOrder="20"
                       showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Auth Token</label>
                    <validate>required-entry</validate>
                </field>

                <field id="verify_service_sid" translate="label" type="text" sortOrder="30"
                       showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Verify Service SID</label>
                    <validate>required-entry</validate>
                </field>
            </group>
        </section>
    </system>
</config>
