<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add
                id="Comave_Integration::comave_integration"
                title="Integration"
                module="Comave_Integration"
                sortOrder="10"
                parent="Comave::top_level"
                resource="Comave_Integration::comave_integration"
        />
        <add
                id="Comave_Integration::comave_integration_traceability"
                title="Traceability"
                module="Comave_Integration"
                sortOrder="10"
                action="comave_integration/traceability/"
                resource="Comave_Integration::comave_integration_traceability"
                parent="Comave_Integration::comave_integration"
                dependsOnConfig="comave_integration/traceability/enable"
        />
        <add
                id="Comave_Integration::comave_integration_traceability_config"
                title="Configuration"
                module="Comave_Integration"
                action="adminhtml/system_config/edit/section/comave_integration"
                sortOrder="20"
                parent="Comave_Integration::comave_integration"
                resource="Comave_Integration::comave_integration_traceability_config"
        />
    </menu>
</config>
