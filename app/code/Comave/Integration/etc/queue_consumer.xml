<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    <consumer name="comave.integration.traceability"
              queue="comave.integration.traceability"
              connection="amqp"
              onlySpawnWhenMessageAvailable="1"
              handler="Comave\Integration\Model\Queue\Consumer\TraceabilityProcessing::execute"/>
</config>
