<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\JwtUserToken\Model\Issuer">
        <plugin name="comave_jwtusertoken_issuer_plugin" type="Comave\Integration\Plugin\JwtUserTokenIssuerPlugin" sortOrder="10" />
    </type>
    <type name="Magento\JwtUserToken\Model\Reader">
        <plugin name="comave_integration_reader_plugin" type="Comave\Integration\Plugin\JwtUserTokenReaderPlugin" sortOrder="10" />
    </type>
    <type name="Comave\SellerApi\Service\RequestHandler">
        <plugin name="comave_integration_service_request_handler_plugin" type="Comave\Integration\Plugin\SellerApiRequestHandlerPlugin" sortOrder="10" />
    </type>

    <preference for="Comave\Integration\Api\TraceabilityMessageInterface" type="Comave\Integration\Model\TraceabilityMessage"/>
    <preference for="Comave\Integration\Api\Data\TraceInterface" type="Comave\Integration\Model\Trace"/>
    <preference for="Comave\Integration\Api\Data\TraceSearchResultInterface" type="Comave\Integration\Model\TraceSearchResults"/>
    <preference for="Comave\Integration\Api\TraceRepositoryInterface" type="Comave\Integration\Model\TraceRepository"/>
    <preference for="Comave\Integration\Api\TraceListRepositoryInterface" type="Comave\Integration\Model\TraceListRepository"/>
    <type name="Magento\Framework\EntityManager\MetadataPool">
        <arguments>
            <argument name="metadata" xsi:type="array">
                <item name="Comave\Integration\Api\Data\TraceInterface" xsi:type="array">
                    <item name="entityTableName" xsi:type="string">comave_integration_traceability</item>
                    <item name="identifierField" xsi:type="string">trace_id</item>
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Comave\Integration\Model\ResourceModel\Trace">
        <arguments>
            <argument name="interfaceClass" xsi:type="string">
                Comave\Integration\Api\Data\TraceInterface
            </argument>
        </arguments>
    </type>
    <type name="Comave\Integration\Model\ResourceModel\Trace\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_integration_traceability</argument>
            <argument name="model" xsi:type="string">Comave\Integration\Model\Trace</argument>
            <argument name="resourceModel" xsi:type="string">
                Comave\Integration\Model\ResourceModel\Trace
            </argument>
            <argument name="idFieldName" xsi:type="string">trace_id</argument>
            <argument name="eventPrefix" xsi:type="string">comave_integration_traceability</argument>
            <argument name="eventObject" xsi:type="string">comave_integration_traceability</argument>
            <argument name="interfaceClass" xsi:type="string">
                Comave\Integration\Api\Data\TraceInterface
            </argument>
        </arguments>
    </type>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="comave_integration_traceability_listing_data_source" xsi:type="string">ComaveIntegrationTraceGridCollection</item>
            </argument>
        </arguments>
    </type>
    <virtualType name="ComaveIntegrationTraceGridCollection" type="Comave\Integration\Model\ResourceModel\Trace\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_integration_traceability</argument>
            <argument name="model" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\Document</argument>
            <argument name="resourceModel" xsi:type="string">Comave\Integration\Model\ResourceModel\Trace</argument>
            <argument name="idFieldName" xsi:type="string">trace_id</argument>
            <argument name="eventPrefix" xsi:type="string">comave_integration_traceability_collection</argument>
            <argument name="eventObject" xsi:type="string">comave_integration_traceability_collection</argument>
            <argument name="interfaceClass" xsi:type="string">Comave\Integration\Api\Data\TraceInterface</argument>
        </arguments>
    </virtualType>
    <virtualType name="TraceabilityProcessingLogger" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">TraceabilityProcessingLogger</argument>
            <argument name="loggerPath" xsi:type="string">traceability_processing</argument>
        </arguments>
    </virtualType>
    <type name="Comave\Integration\Model\Queue\Consumer\TraceabilityProcessing">
        <arguments>
            <argument xsi:type="object" name="logger">TraceabilityProcessingLogger</argument>
        </arguments>
    </type>
</config>
