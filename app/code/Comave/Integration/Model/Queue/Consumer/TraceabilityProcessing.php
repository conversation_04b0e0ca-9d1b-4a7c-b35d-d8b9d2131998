<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Model\Queue\Consumer;

use Comave\Integration\Api\TraceabilityMessageInterface;
use Comave\Integration\Model\TraceUiManager;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

class TraceabilityProcessing
{
    public const string TRACEABILITY_TOPIC_NAME = 'comave.integration.traceability.topic';

    /**
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Comave\Integration\Model\TraceUiManager $traceUiManager
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly TraceUiManager $traceUiManager,
    ) {
    }

    /**
     * @param \Comave\Integration\Api\TraceabilityMessageInterface $message
     * @return void
     */
    public function execute(TraceabilityMessageInterface $message): void
    {
        $method = $message->getMethod();
        $endpoint = $message->getEndpoint();
        if (empty($method) || empty($endpoint)) {
            $this->logger->warning(
                'Failed to retrieved full integration trace message.',
                [
                    'queueMessage' => $message->getData(),
                ]
            );

            return;
        }

        try {
            $trace = $this->traceUiManager->get();
            $trace->setMethod($method);
            $trace->setEndpoint($endpoint);
            $trace->setPayload($message->getPayload() ?? '{}');
            $this->traceUiManager->save($trace);
        } catch (LocalizedException $exception) {
            $this->logger->warning(
                $exception->getMessage(),
                [
                    'queueMessage' => $message->getData(),
                ]
            );
        }
    }
}
