<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Model;

use Comave\Integration\Api\Data\TraceInterface;
use Comave\Integration\Model\ResourceModel\Trace as TraceResourceModel;
use Magento\Framework\Api\ExtensionAttributesInterface;
use Magento\Framework\Model\AbstractExtensibleModel;

class Trace extends AbstractExtensibleModel implements TraceInterface
{
    /**
     * Cache tag
     *
     * @var string
     */
    public const string CACHE_TAG = 'comave_integration_traceability';
    /**
     * Cache tag
     *
     * @var string
     * phpcs:disable PSR2.Classes.PropertyDeclaration.Underscore,PSR12.Classes.PropertyDeclaration.Underscore
     */
    protected $_cacheTag = self::CACHE_TAG;
    /**
     * Event prefix
     *
     * @var string
     */
    protected $_eventPrefix = 'comave_integration_traceability';
    /**
     * Event object
     *
     * @var string
     */
    protected $_eventObject = 'comave_integration_traceability';
    //phpcs:enable

    /**
     * Get identities
     *
     * @return array
     */
    public function getIdentities(): array
    {
        return [sprintf("%s_%s", self::CACHE_TAG, $this->getId())];
    }

    /**
     * @return \Magento\Framework\Api\ExtensionAttributesInterface|null
     */
    public function getExtensionAttributes(): ?ExtensionAttributesInterface
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * @param \Magento\Framework\Api\ExtensionAttributesInterface $extensionAttributes
     * @return \Comave\Integration\Model\Trace
     */
    public function setExtensionAttributes(ExtensionAttributesInterface $extensionAttributes): Trace
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * Initialize resource model
     *
     * @return void
     * phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init(TraceResourceModel::class);
    }

    /**
     * @param int $id
     * @return \Comave\Integration\Api\Data\TraceInterface
     */
    public function setTraceId(int $id): TraceInterface
    {
        return $this->setData(self::TRACE_ID, $id);
    }

    /**
     * @return int
     */
    public function getTraceId(): int
    {
        return (int)$this->getData(self::TRACE_ID);
    }

    /**
     * @return null|string
     */
    public function getMethod(): ?string
    {
        return $this->getData(self::METHOD);
    }

    /**
     * @param string $method
     * @return self
     */
    public function setMethod(string $method): TraceInterface
    {
        return $this->setData(self::METHOD, $method);
    }

    /**
     * @return string|null
     */
    public function getEndpoint(): ?string
    {
        return $this->getData(self::ENDPOINT);
    }

    /**
     * @param string $endpoint
     * @return self
     */
    public function setEndpoint(string $endpoint): TraceInterface
    {
        return $this->setData(self::ENDPOINT, $endpoint);
    }

    /**
     * @param string $payload
     * @return self
     */
    public function setPayload(string $payload): TraceInterface
    {
        return $this->setData(self::PAYLOAD, $payload);
    }

    /**
     * Get Tracking Payload
     *
     * @return string|null
     */
    public function getPayload(): ?string
    {
        return $this->getData(self::PAYLOAD);
    }
}
