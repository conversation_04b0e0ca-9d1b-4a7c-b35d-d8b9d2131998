<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Model;

use Comave\Integration\Api\Data\TraceInterface;
use Comave\Integration\Api\TraceabilityMessageInterface;
use Magento\Framework\DataObject;

class TraceabilityMessage extends DataObject implements TraceabilityMessageInterface
{

    /**
     * @param string $method
     * @return self
     */
    public function setMethod(string $method): TraceabilityMessageInterface
    {
        return $this->setData(TraceInterface::METHOD, $method);
    }

    /**
     * @return string
     */
    public function getMethod(): string
    {
        return $this->getData(TraceInterface::METHOD);
    }

    /**
     * @param string $endpoint
     * @return self
     */
    public function setEndpoint(string $endpoint): TraceabilityMessageInterface
    {
        return $this->setData(TraceInterface::ENDPOINT, $endpoint);
    }

    /**
     * @return string
     */
    public function getEndpoint(): string
    {
        return $this->getData(TraceInterface::ENDPOINT);
    }

    /**
     * @return string|null
     */
    public function getPayload(): ?string
    {
        return $this->getData(TraceInterface::PAYLOAD);
    }

    /**
     * @param string|null $payload
     * @return self
     */
    public function setPayload(?string $payload): TraceabilityMessageInterface
    {
        return $this->setData(TraceInterface::PAYLOAD, $payload);
    }
}
