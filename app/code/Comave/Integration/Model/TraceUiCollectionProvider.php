<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Model;

use Comave\Integration\Model\ResourceModel\Trace\CollectionFactory;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Umc\Crud\Ui\CollectionProviderInterface;

class TraceUiCollectionProvider implements CollectionProviderInterface
{
    /**
     * @param \Comave\Integration\Model\ResourceModel\Trace\CollectionFactory $factory
     */
    public function __construct(private readonly CollectionFactory $factory)
    {
    }

    /**
     * @return \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
     */
    public function getCollection(): AbstractCollection
    {
        return $this->factory->create();
    }
}
