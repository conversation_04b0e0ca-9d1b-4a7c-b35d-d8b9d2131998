<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Model;

use Comave\Integration\Api\Data\TraceInterface;
use Comave\Integration\Api\TraceListRepositoryInterface;
use Comave\Integration\Api\TraceRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Model\AbstractModel;
use Umc\Crud\Ui\EntityUiManagerInterface;

class TraceUiManager implements EntityUiManagerInterface
{
    /**
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     * @param \Comave\Integration\Api\TraceListRepositoryInterface $listRepository
     * @param \Comave\Integration\Api\TraceRepositoryInterface $repository
     * @param \Comave\Integration\Model\TraceFactory $factory
     */
    public function __construct(
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly TraceListRepositoryInterface $listRepository,
        private readonly TraceRepositoryInterface $repository,
        private readonly TraceFactory $factory
    ) {
    }

    /**
     * @param \Magento\Framework\Model\AbstractModel $trace
     * @return void
     */
    public function save(AbstractModel $trace)
    {
        $this->repository->save($trace);
    }

    /**
     * @param int $id
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(int $id)
    {
        $this->repository->deleteById($id);
    }

    /**
     * @param int|null $id
     * @return \Magento\Framework\Model\AbstractModel | Trace | TraceInterface;
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(?int $id = null)
    {
        return ($id)
            ? $this->repository->get($id)
            : $this->factory->create();
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(): array
    {
        return $this->listRepository->getList($this->searchCriteriaBuilder->create())->getItems();
    }
}
