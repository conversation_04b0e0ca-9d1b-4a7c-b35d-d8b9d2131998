<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Api\Data;

use Magento\Framework\Api\ExtensibleDataInterface;

interface TraceInterface extends ExtensibleDataInterface
{
    public const string TRACE_ID = 'trace_id';
    public const string METHOD = 'method';
    public const string ENDPOINT = 'endpoint';
    public const string PAYLOAD = 'payload';

    /**
     * @param $id
     * @return self
     */
    public function setId($id);

    /**
     * @return int
     */
    public function getId();

    /**
     * @param int $id
     * @return self
     */
    public function setTraceId(int $id): self;

    /**
     * @return int|null
     */
    public function getTraceId(): ?int;


    /**
     * @return string|null
     */
    public function getMethod(): ?string;

    /**
     * @param string $method
     * @return self
     */
    public function setMethod(string $method): self;

    /**
     * @return string|null
     */
    public function getEndpoint(): ?string;

    /**
     * @param string $endpoint
     * @return self
     */
    public function setEndpoint(string $endpoint): self;

    /**
     * @param string $payload
     * @return self
     */
    public function setPayload(string $payload): self;

    /**
     * Get Tracking Payload
     *
     * @return string|null
     */
    public function getPayload(): ?string;
}
