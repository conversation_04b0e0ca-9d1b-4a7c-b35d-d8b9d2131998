<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Integration\Api;

interface TraceabilityMessageInterface
{
    /**
     * @param string $method
     * @return self
     */
    public function setMethod(string $method): self;

    /**
     * @return string
     */
    public function getMethod(): string;

    /**
     * @param string $endpoint
     * @return self
     */
    public function setEndpoint(string $endpoint): self;

    /**
     * @return string
     */
    public function getEndpoint(): string;

    /**
     * @return string|null
     */
    public function getPayload(): ?string;

    /**
     * @param string|null $payload
     * @return self
     */
    public function setPayload(?string $payload): self;
}
