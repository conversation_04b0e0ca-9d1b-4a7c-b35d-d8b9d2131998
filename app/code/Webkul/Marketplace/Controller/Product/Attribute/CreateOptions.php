<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
declare(strict_types=1);

namespace Webkul\Marketplace\Controller\Product\Attribute;

use Webkul\Marketplace\Helper\Data as HelperData;
use Magento\Framework\Json\Helper\Data as JsonHelper;
use Magento\Framework\App\Action\Context;
use Magento\Catalog\Model\ResourceModel\Eav\AttributeFactory;
use Comave\Marketplace\Plugin\RestrictAddAttributeOption;
use Magento\Framework\Exception\LocalizedException;
use Comave\Marketplace\Service\SellerAttributePermission;

/**
 * Webkul Marketplace Product Attribute CreateOptions Controller.
 */
class CreateOptions extends \Magento\Framework\App\Action\Action
{
    public function __construct(
        Context $context,
        private readonly AttributeFactory $eavAttribute,
        private readonly HelperData $helper,
        private readonly JsonHelper $jsonHelper,
    ) {
        parent::__construct($context);
    }

    /**
     * Create attribute new options.
     *
     * @return json data
     */
    public function execute()
    {
        if (!$this->helper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        try {
            $savedOptionsArray = [];
            $optionsData = (array) $this->getRequest()->getParam('options', []);

            foreach ($optionsData as $option) {
                if (isset($option['attribute_id']) && isset($option['label'])) {
                    $attributeId = $option['attribute_id'];
                    $eavAttributeColl = $this->eavAttribute->create()->load($attributeId);

                    $isEditable = SellerAttributePermission::isAttributeEditable($eavAttributeColl);
                    if ($isEditable !== SellerAttributePermission::STATUS_DISABLED) {
                        $this->messageManager->addErrorMessage(__('You are not allowed to update this attribute.'));

                        return $this->resultRedirectFactory->create()->setUrl(
                            $this->_redirect->getRefererUrl()
                        );
                    }

                    $optionsCount = count($eavAttributeColl->getSource()->getAllOptions(false));
                    $eavAttributeColl->setOption(
                        [
                            'value' => ['option_0' => [$option['label']]],
                            'order' => ['option_0' => $optionsCount++],
                        ]
                    );
                    $eavAttributeColl->save();
                    $allOptionsArr = $eavAttributeColl->getSource()->getAllOptions(false);
                    $createdOptionArr = array_pop($allOptionsArr);
                    $savedOptionsArray[$option['id']] = $createdOptionArr['value'];
                }
            }
            return $this->getResponse()->representJson(
                $this->jsonHelper->jsonEncode($savedOptionsArray)
            );
        } catch (\Exception $e) {
            $this->helper->logDataInLogger(
                "Controller_Product_Attribute_CreateOptions execute : ".$e->getMessage()
            );
            $this->getResponse()->representJson(
                $this->jsonHelper->jsonEncode(
                    [
                        'error' => $e->getMessage(),
                        'errorcode' => $e->getCode(),
                    ]
                )
            );
        }
    }
}
