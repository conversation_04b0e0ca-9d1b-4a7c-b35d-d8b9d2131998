<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Ui/etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">marketplace_products_list.marketplace_products_list_data_source</item>
            <item name="deps" xsi:type="string">marketplace_products_list.marketplace_products_list_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">marketplace_products_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="add" xsi:type="array">
                <item name="name" xsi:type="string">Sync Product</item>
                <item name="label" xsi:type="string" translate="true">Sync Product</item>
                <item name="url" xsi:type="string">sync/index/Sync</item>
                <item name="class" xsi:type="string">primary</item>
            </item>
        </item>
    </argument>
    <dataSource name="marketplace_products_list_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">marketplace_products_list_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
            </item>
        </argument>
    </dataSource>
    <container name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="template" xsi:type="string">ui/grid/toolbar</item>
            </item>
        </argument>
        <bookmark name="bookmarks">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="storageConfig" xsi:type="array">
                        <item name="namespace" xsi:type="string">marketplace_products_list</item>
                    </item>
                </item>
            </argument>
        </bookmark>
        <component name="columns_controls">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="columnsData" xsi:type="array">
                        <item name="provider" xsi:type="string">marketplace_products_list.marketplace_products_list.marketplace_products_columns</item>
                    </item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/controls/columns</item>
                    <item name="displayArea" xsi:type="string">dataGridActions</item>
                </item>
            </argument>
        </component>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="columnsProvider" xsi:type="string">marketplace_products_list.marketplace_products_list.marketplace_products_columns</item>
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">marketplace_products_list.marketplace_products_list.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.filters</item>
                    </item>
                    <item name="templates" xsi:type="array">
                        <item name="filters" xsi:type="array">
                            <item name="select" xsi:type="array">
                                <item name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</item>
                                <item name="template" xsi:type="string">ui/grid/filters/elements/ui-select</item>
                            </item>
                        </item>
                    </item>
                    <item name="childDefaults" xsi:type="array">
                        <item name="provider" xsi:type="string">marketplace_products_list.marketplace_products_list.listing_top.listing_filters</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">marketplace_products_list.marketplace_products_list.marketplace_products_columns.${ $.index }:visible</item>
                        </item>
                    </item>
                </item>
                <item name="observers" xsi:type="array">
                    <item name="column" xsi:type="string">column</item>
                </item>
            </argument>
        </filters>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">marketplace_products_list.marketplace_products_list.marketplace_products_columns.ids</item>
                    <item name="indexField" xsi:type="string">entity_id</item>
                </item>
            </argument>
            <action name="disapprove">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">disapprove</item>
                        <item name="label" xsi:type="string" translate="true">Disapprove</item>
                        <item name="url" xsi:type="url" path="marketplace/product/massDisapprove"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Disapprove</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you want to disapprove selected items?</item>
                        </item>
                    </item>
                </argument>
            </action>
            <action name="approve">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">approve</item>
                        <item name="label" xsi:type="string" translate="true">Approve</item>
                        <item name="url" xsi:type="url" path="marketplace/product/massApprove"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Approve</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you want to approve selected items?</item>
                        </item>
                    </item>
                </argument>
            </action>
        </massaction>
        <paging name="listing_paging">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">marketplace_products_list.marketplace_products_list.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.paging</item>
                    </item>•••••••••
                    <item name="selectProvider" xsi:type="string">marketplace_products_list.marketplace_products_list.marketplace_products_columns.ids</item>
                </item>
            </argument>
        </paging>
    </container>
    <columns name="marketplace_products_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/listing</item>
                <item name="editorConfig" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">marketplace_products_list.marketplace_products_list.marketplace_products_columns.ids</item>
                    <item name="enabled" xsi:type="boolean">true</item>
                    <item name="indexField" xsi:type="string">mageproduct_id</item>
                    <item name="clientConfig" xsi:type="array">
                        <item name="saveUrl" xsi:type="url" path="marketplace/product/inlineEdit"/>
                        <item name="validateBeforeSave" xsi:type="boolean">false</item>
                    </item>
                </item>
                <item name="childDefaults" xsi:type="array">
                    <item name="fieldAction" xsi:type="array">
                        <item name="provider" xsi:type="string">marketplace_products_list.marketplace_products_list.marketplace_products_columns_editor</item>
                        <item name="target" xsi:type="string">startEdit</item>
                        <item name="params" xsi:type="array">
                            <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                            <item name="1" xsi:type="boolean">true</item>
                        </item>
                    </item>
                </item>
                
                
            </item>
            </argument>
        <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="resizeEnabled" xsi:type="boolean">false</item>
                    <item name="resizeDefaultWidth" xsi:type="string">55</item>
                    <item name="indexField" xsi:type="string">entity_id</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="entity_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="sorting" xsi:type="string">asc</item>
                    <item name="label" xsi:type="string" translate="true">ID</item>
                    <item name="sortOrder" xsi:type="number">1</item>
                </item>
            </argument>
        </column>
        <column name="mageproduct_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Product ID</item>
                    <item name="sortOrder" xsi:type="number">2</item>
                </item>
            </argument>
        </column>
        <column name="name" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Customerlink">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/link</item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Seller Name</item>
                    <item name="viewUrlPath" xsi:type="string">customer/view</item>
                    <item name="urlEntityParamName" xsi:type="string">seller_id</item>
                    <item name="sortOrder" xsi:type="number">3</item>
                </item>
            </argument>
        </column>
        <column name="product_name" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Productlink">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/link</item>
                    <item name="label" xsi:type="string" translate="true">Product Name</item>
                    <item name="sortOrder" xsi:type="number">4</item>
                    <item name="filter" xsi:type="string">text</item>
                </item>
            </argument>
        </column>
         <column name="flagcount" class="Webkul\Marketplace\Ui\Component\Listing\Columns\ProductFlaglink">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/link</item>
                    <item name="label" xsi:type="string" translate="true">Flags</item>
                    <item name="sortOrder" xsi:type="number">5</item>
                    <item name="filter" xsi:type="string">false</item>
                </item>
            </argument>
        </column>
        <column name="product_price" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Price">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Price</item>
                    <item name="sortOrder" xsi:type="number">6</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">price</item>
                        <item name="validation" xsi:type="array">
                            <item name="validate-zero-or-greater" xsi:type="boolean">true</item>
                        </item>
                    </item>
                </item>
            </argument>
        </column>
        <column name="qty">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Quantity</item>
                    <item name="sortOrder" xsi:type="number">7</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="validate-zero-or-greater" xsi:type="boolean">true</item>
                        </item>
                    </item>
                </item>
            </argument>
        </column>
        <column name="is_approved">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webkul\Marketplace\Model\Product\Source\Status</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <!-- <item name="editor" xsi:type="string">select</item> -->
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Status</item>
                    <item name="sortOrder" xsi:type="number">8</item>
                </item>
            </argument>
        </column>
        <column name="status">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Magento\Catalog\Model\Product\Attribute\Source\Status</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="editor" xsi:type="string">select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Product Status</item>
                    <item name="sortOrder" xsi:type="number">9</item>
                </item>
            </argument>
        </column>
        <column name="mage_store_id" class="Webkul\Marketplace\Ui\Component\Listing\Columns\StoreView">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Store View</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">dateRange</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/date</item>
                    <item name="dataType" xsi:type="string">date</item>
                    <item name="label" xsi:type="string" translate="true">Created</item>
                    <item name="sortOrder" xsi:type="number">11</item>
                </item>
            </argument>
        </column>
        <column name="updated_at" class="Magento\Ui\Component\Listing\Columns\Date">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">dateRange</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/date</item>
                    <item name="dataType" xsi:type="string">date</item>
                    <item name="label" xsi:type="string" translate="true">Modified</item>
                    <item name="sortOrder" xsi:type="number">12</item>
                </item>
            </argument>
        </column>
        <actionsColumn name="pro_prev" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Thumbnail">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/thumbnail</item>
                    <item name="indexField" xsi:type="string">mageproduct_id</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="label" xsi:type="string" translate="true">Preview</item>
                    <item name="sortOrder" xsi:type="number">13</item>
                </item>
            </argument>
        </actionsColumn>
        <actionsColumn name="pro_deny" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Prodeny">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/prodeny</item>
                    <item name="indexField" xsi:type="string">mageproduct_id</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="label" xsi:type="string" translate="true">Deny</item>
                    <item name="sortOrder" xsi:type="number">14</item>
                </item>
            </argument>
        </actionsColumn>
        <actionsColumn name="product_view" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Proview">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/link</item>
                    <item name="label" xsi:type="string" translate="true">Product View</item>
                    <item name="sortOrder" xsi:type="number">15</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                </item>
            </argument>
        </actionsColumn>
    </columns>
</listing>
