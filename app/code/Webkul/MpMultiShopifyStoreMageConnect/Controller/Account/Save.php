<?php
/**
 * @category   Webkul
 * @package    Webkul_MpMultiShopifyStoreMageConnect
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
namespace Webkul\MpMultiShopifyStoreMageConnect\Controller\Account;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;

class Save extends Action
{
    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    private $resultPageFactory;

    /**
     * @var /Webkul\MpMultiShopifyStoreMageConnect\Model/ShopifyaccountsFactory
     */
    private $shopifyAccountsFactory;

    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Helper\Data
     */
    private $dataHelper;

    /**
     * @var \Magento\Framework\Encryption\EncryptorInterface
     */
    private $encryptor;

    /**
     * @var \Webkul\Marketplace\Helper\Data
     */
    private $mphelper;

    /**
     * Construct function
     *
     * @param Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param \Webkul\MpMultiShopifyStoreMageConnect\Model\ShopifyaccountsFactory $shopifyAccountsFactory
     * @param \Webkul\Marketplace\Helper\Data $mphelper
     * @param \Webkul\MpMultiShopifyStoreMageConnect\Helper\Data $dataHelper
     * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
     */
    public function __construct(
        Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        \Webkul\MpMultiShopifyStoreMageConnect\Model\ShopifyaccountsFactory $shopifyAccountsFactory,
        \Webkul\Marketplace\Helper\Data $mphelper,
        \Webkul\MpMultiShopifyStoreMageConnect\Helper\Data $dataHelper,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->shopifyAccountsFactory = $shopifyAccountsFactory;
        $this->mphelper = $mphelper;
        $this->dataHelper = $dataHelper;
        $this->encryptor = $encryptor;
    }

    /**
     * Shopify account details
     *
     * @return Page
     */
    public function execute()
    {
        $flag = false;
        $reserveId = 0;
        $resultRedirect = $this->resultRedirectFactory->create();
        $temp = $parameters = $this->getRequest()->getParams();
        if ($temp['shopify_app_select']==1) {
            if ($this->getRequest()->getParam('id') && $this->getRequest()->getParam('front')) {
                $previousAccessToken = $this->getAccessTokenFromDbToVerify($this->getRequest()->getParam('id'));
                $temp['access_token'] = $previousAccessToken == $parameters['access_token'] ?
                $this->encryptor->decrypt($parameters['access_token']) : $parameters['access_token'];
            }
            $status = $this->checkForAccessTokenEmptyValue($parameters);
        } else {
            if ($this->getRequest()->getParam('id') && $this->getRequest()->getParam('front')) {
                $previousApiKey = $this->getTheApiKeyFromDbToVerify($this->getRequest()->getParam('id'));
                $previousApiPwd = $this->getTheApiPwdFromDbToVerify($this->getRequest()->getParam('id'));
                $temp['shopify_api_key'] = $previousAccessToken == $parameters['shopify_api_key'] ?
                    $this->encryptor->decrypt($parameters['shopify_api_key']) : $parameters['shopify_api_key'];
                $temp['shopify_pwd'] = $previousAccessToken == $parameters['shopify_pwd'] ?
                    $this->encryptor->decrypt($parameters['shopify_pwd']) : $parameters['shopify_pwd'];
            }
            $status = $this->checkForEmptyValue($parameters);
        }
        $emptyValueCheck = ['status'=>'', 'msg'=> ''];
        if ($status['status']) {
            $data = $this->dataHelper->authorizeShopifyShop($temp);
            $data['magento_base_currency'] = $this->dataHelper->getBaseCurrencyCode();
            $error = __('Shopify user didn\'t authorize successfully, Please try again.');
            if ($data['status'] && $data['api_request_http_code'] == 200) {
                if (isset($data['product_type_allowed']) && $data['product_type_allowed']) {
                    $data['product_type_allowed'] = implode(',', $data['product_type_allowed']);
                }

                if (empty($data['product_type_allowed'])) {
                    $data['product_type_allowed'] = 'simple,configurable';
                }

                $data['access_token'] = $this->encryptor->encrypt($data['access_token']);
                $model = $shopifyAccountsCollection = $this->shopifyAccountsFactory->create();
                $sellerId = $this->mphelper->getSellerData()->getFirstItem()->getSellerId();
                $data["seller_id"] = $sellerId;
                if ($this->getRequest()->getParam('id') && $this->getRequest()->getParam('front')) {
                    $data["entity_id"] = $this->getRequest()->getParam('id');
                }
                $model->addData($data)->save();
                $this->messageManager
                    ->addSuccess(
                        __('Shopify details saved successfully, Now edit the record for syncronization process')
                    );
                // if (isset($temp['id']) && isset($temp['back'])) {
                //     $this->_redirect('*/*/edit', ['id'=>$temp['id']]);
                // } else {
                    return $resultRedirect->setPath('*/*/connect');
                // }
            } else {
                $this->messageManager->addError($data['error_msg']);
                return $resultRedirect->setPath('*/*/connect');
            }
        } else {
            $this->messageManager->addError(__('Something went wrong'));
            return $resultRedirect->setPath('*/*/connect');
        }
    }

    /**
     * GetAccessTokenFromDbToVerify function get the api key
     *
     * @param int $id
     * @return string
     */
    private function getAccessTokenFromDbToVerify($id = '')
    {
        $modal = $this->shopifyAccountsFactory->create()->load($id);
        return $modal->getAccessToken();
    }

    /**
     * GetTheApiKeyFromDbToVerify function get the api key
     *
     * @param int $id
     * @return string
     */
    private function getTheApiKeyFromDbToVerify($id = '')
    {
        $modal = $this->shopifyAccountsFactory->create()->load($id);
        return $modal->getShopifyApiKey();
    }

    /**
     * GetTheApiPwdFromDbToVerify function get the api password
     *
     * @param int $id
     * @return string
     */
    private function getTheApiPwdFromDbToVerify($id = '')
    {
        $modal = $this->shopifyAccountsFactory->create()->load($id);
        return $modal->getShopifyPwd();
    }

    /**
     * BeforeSave function check that value is obscure or not
     *
     * @param string $value
     * @return array
     */
    private function beforeSave($value)
    {
        if (!preg_match('/^\*+$/', $value) && !empty($value)) {
            $encrypted = $this->encryptor->encrypt($value);
            return ['value' => $encrypted, 'status' => true];
        } elseif (empty($value)) {
            return ['value' => $value, 'status' => false];
        }
        return ['value' => $value, 'status' => false];
    }

     /**
      * CheckForAccessTokenEmptyValue function check for empty value
      *
      * @param array $parameters
      * @return array
      */
    private function checkForAccessTokenEmptyValue($parameters)
    {
        if ($parameters['access_token'] == '' ||
            $parameters['shopify_domain_name'] == ''
        ) {
            return ['status' => 0, 'msg'=>'requird fields are empty'];
        }
        return ['status'=> 1, 'msg'=>''];
    }

    /**
     * CheckForEmptyValue function check for empty value
     *
     * @param array $parameters
     * @return array
     */
    private function checkForEmptyValue($parameters)
    {
        if ($parameters['shopify_api_key'] == '' ||
            $parameters['shopify_pwd'] == '' ||
            $parameters['shopify_domain_name'] == ''
        ) {
            return ['status' => 0, 'msg'=>'requird fields are empty'];
        }
        return ['status'=> 1, 'msg'=>''];
    }
}
