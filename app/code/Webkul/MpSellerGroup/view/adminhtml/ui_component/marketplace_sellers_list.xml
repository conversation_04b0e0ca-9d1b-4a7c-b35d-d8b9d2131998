<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerGroup
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
 -->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <!-- <container name="listing_top"> -->
    <listingToolbar name="listing_top">
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/tree-massactions</item>
                </item>
            </argument>

            <action name="assign_to_group">
                <settings>
                    <type>assign_to_group</type>
                    <label translate="true">Assign a Seller Group</label>
                    <actions class="Webkul\MpSellerGroup\Ui\Component\MassAction\Group\Options"/>
                </settings>
            </action>

            <action name="unassign_from_group">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">unassign_from_group</item>
                        <item name="label" xsi:type="string" translate="true">Unassign Seller From Group</item>
                        <item name="url" xsi:type="url" path="mpsellergroup/group/massunassign"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Unassign Seller From Group</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you want to unassign seller from group?</item>
                        </item>
                    </item>
                </argument>
            </action>
        </massaction>
    </listingToolbar>
    <!-- </container> -->
    <columns name="marketplace_sellers_columns">
        <column name="group_code" class="Webkul\MpSellerGroup\Ui\Component\Listing\Columns\AssignedGroup">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="label" xsi:type="string" translate="true">Assigned Group</item>
                    <item name="sortOrder" xsi:type="number">5</item>
                </item>
            </argument>
        </column>
    </columns>

</listing>
